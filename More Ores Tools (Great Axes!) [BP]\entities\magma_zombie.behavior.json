//bridge-file-version: #2
{
	"format_version": "1.16.0",
	"minecraft:entity": {
		"description": {
			"identifier": "oh:magma_zombie",
			"is_spawnable": true,
			"is_summonable": true,
			"is_experimental": false
		},
		"component_groups": {
			"minecraft:look_to_start_drowned_transformation": {
				"minecraft:environment_sensor": {
					"triggers": {
						"filters": {
							"test": "is_underwater",
							"subject": "self",
							"operator": "==",
							"value": true
						},
						"event": "minecraft:start_transforming"
					}
				}
			},
			"minecraft:start_drowned_transformation": {
				"minecraft:environment_sensor": {
					"triggers": {
						"filters": {
							"test": "is_underwater",
							"subject": "self",
							"operator": "==",
							"value": false
						},
						"event": "minecraft:stop_transforming"
					}
				},
				"minecraft:timer": {
					"looping": false,
					"time": 30,
					"time_down_event": {
						"event": "minecraft:convert_to_drowned"
					}
				}
			},
			"minecraft:convert_to_drowned": {
				"minecraft:transformation": {
					"into": "minecraft:drowned<minecraft:as_adult>",
					"transformation_sound": "convert_to_drowned",
					"drop_equipment": true,
					"delay": {
						"value": 15
					}
				},
				"minecraft:is_shaking": {}
			},
			"minecraft:convert_to_baby_drowned": {
				"minecraft:transformation": {
					"into": "minecraft:drowned<minecraft:as_baby>",
					"transformation_sound": "convert_to_drowned",
					"drop_equipment": true,
					"delay": {
						"value": 15
					}
				},
				"minecraft:is_shaking": {}
			},
			"minecraft:zombie_baby": {
				"minecraft:experience_reward": {
					"on_death": "query.last_hit_by_player?12+(query.equipment_count*Math.Random(1,3)):0"
				},
				"minecraft:is_baby": {},
				"minecraft:scale": {
					"value": 0.6
				},
				"minecraft:movement": {
					"value": 0.35
				}
			},
			"minecraft:zombie_adult": {
				"minecraft:experience_reward": {
					"on_death": "query.last_hit_by_player?5+(query.equipment_count*Math.Random(1,3)):0"
				},
				"minecraft:movement": {
					"value": 0.25
				},
				"minecraft:rideable": {
					"seat_count": 1,
					"family_types": [
						"zombie"
					],
					"seats": {
						"position": [
							0,
							1.1,
							-0.35
						],
						"lock_rider_rotation": 0
					}
				},
				"minecraft:behavior.mount_pathing": {
					"priority": 2,
					"speed_multiplier": 1.25,
					"target_dist": 0,
					"track_target": true
				}
			},
			"minecraft:zombie_jockey": {
				"minecraft:behavior.find_mount": {
					"priority": 1,
					"within_radius": 16,
					"start_delay": 15,
					"max_failed_attempts": 20
				}
			},
			"minecraft:can_have_equipment": {
				"minecraft:equipment": {
					"table": "loot_tables/entities/zombie_equipment.json"
				}
			},
			"bridge:execute_command_id_1": {
				"minecraft:skin_id": {
					"value": 1
				}
			},
			"bridge:execute_no_command": {
				"minecraft:skin_id": {
					"value": 0
				}
			}
		},
		"components": {
			"minecraft:behavior.avoid_block": {
				"priority": 1,
				"tick_interval": 1,
				"search_range": 3,
				"search_height": 3,
				"target_selection_method": "nearest",
				"target_blocks": [
					"minecraft:flowing_water",
					"minecraft:water"
				]
			},
			"minecraft:nameable": {},
			"minecraft:type_family": {
				"family": [
					"zombie",
					"magma_zombie",
					"undead",
					"monster",
					"mob"
				]
			},
			"minecraft:equip_item": {},
			"minecraft:collision_box": {
				"width": 0.6,
				"height": 1.9
			},
			"minecraft:movement.basic": {},
			"minecraft:navigation.walk": {
				"is_amphibious": true,
				"can_pass_doors": true,
				"can_walk": true,
				"can_break_doors": true
			},
			"minecraft:annotation.break_door": {},
			"minecraft:jump.static": {},
			"minecraft:can_climb": {},
			"minecraft:health": {
				"value": 30,
				"max": 40
			},
			"minecraft:hurt_on_condition": {
				"damage_conditions": [
					{
						"filters": {
							"test": "in_contact_with_water",
							"operator": "==",
							"value": true
						},
						"cause": "drowning",
						"damage_per_tick": 1
					}
				]
			},
			"minecraft:breathable": {
				"total_supply": 15,
				"suffocate_time": 0,
				"breathes_air": true,
				"breathes_water": true
			},
			"minecraft:fire_immune": {
			},
			"minecraft:attack": {
				"damage": 4
			},
			"minecraft:loot": {
				"table": "loot_tables/entities/zombie.json"
			},
			"minecraft:shareables": {
				"items": [
					{
						"item": "minecraft:netherite_sword",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 0
					},
					{
						"item": "minecraft:diamond_sword",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 1
					},
					{
						"item": "minecraft:iron_sword",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 2
					},
					{
						"item": "minecraft:stone_sword",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 3
					},
					{
						"item": "minecraft:golden_sword",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 4
					},
					{
						"item": "minecraft:wooden_sword",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 5
					},
					{
						"item": "minecraft:netherite_helmet",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 0
					},
					{
						"item": "minecraft:diamond_helmet",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 1
					},
					{
						"item": "minecraft:iron_helmet",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 2
					},
					{
						"item": "minecraft:chainmail_helmet",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 3
					},
					{
						"item": "minecraft:golden_helmet",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 4
					},
					{
						"item": "minecraft:leather_helmet",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 5
					},
					{
						"item": "minecraft:turtle_helmet",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 6
					},
					{
						"item": "minecraft:skull:0",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 7
					},
					{
						"item": "minecraft:skull:1",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 7
					},
					{
						"item": "minecraft:carved_pumpkin",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 7
					},
					{
						"item": "minecraft:netherite_chestplate",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 0
					},
					{
						"item": "minecraft:diamond_chestplate",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 1
					},
					{
						"item": "minecraft:iron_chestplate",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 2
					},
					{
						"item": "minecraft:chainmail_chestplate",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 3
					},
					{
						"item": "minecraft:golden_chestplate",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 4
					},
					{
						"item": "minecraft:leather_chestplate",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 5
					},
					{
						"item": "minecraft:netherite_leggings",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 0
					},
					{
						"item": "minecraft:diamond_leggings",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 1
					},
					{
						"item": "minecraft:iron_leggings",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 2
					},
					{
						"item": "minecraft:chainmail_leggings",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 3
					},
					{
						"item": "minecraft:golden_leggings",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 4
					},
					{
						"item": "minecraft:leather_leggings",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 5
					},
					{
						"item": "minecraft:netherite_boots",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 0
					},
					{
						"item": "minecraft:diamond_boots",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 1
					},
					{
						"item": "minecraft:iron_boots",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 2
					},
					{
						"item": "minecraft:chainmail_boots",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 3
					},
					{
						"item": "minecraft:golden_boots",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 4
					},
					{
						"item": "minecraft:leather_boots",
						"want_amount": 1,
						"surplus_amount": 1,
						"priority": 5
					}
				]
			},
			"minecraft:environment_sensor": {
				"triggers": {
					"filters": {
						"test": "is_underwater",
						"operator": "==",
						"value": true
					},
					"event": "minecraft:start_transforming"
				}
			},
			"minecraft:despawn": {
				"despawn_from_distance": {}
			},
			"minecraft:behavior.equip_item": {
				"priority": 2
			},
			"minecraft:behavior.melee_attack": {
				"priority": 3,
				"on_attack": {
					"event": "attack"
				}
			},
			"minecraft:behavior.stomp_turtle_egg": {
				"priority": 4,
				"speed_multiplier": 1,
				"search_range": 10,
				"search_height": 2,
				"goal_radius": 1.14,
				"interval": 20
			},
			"minecraft:behavior.pickup_items": {
				"priority": 6,
				"max_dist": 3,
				"goal_radius": 2,
				"speed_multiplier": 1,
				"pickup_based_on_chance": true,
				"can_pickup_any_item": true
			},
			"minecraft:behavior.random_stroll": {
				"priority": 7,
				"speed_multiplier": 1
			},
			"minecraft:behavior.look_at_player": {
				"priority": 8,
				"look_distance": 6,
				"probability": 0.02
			},
			"minecraft:behavior.random_look_around": {
				"priority": 9
			},
			"minecraft:behavior.hurt_by_target": {
				"priority": 1
			},
			"minecraft:behavior.nearest_attackable_target": {
				"priority": 2,
				"must_see": true,
				"reselect_targets": true,
				"within_radius": 25,
				"must_see_forget_duration": 17,
				"entity_types": [
					{
						"filters": {
							"any_of": [
								{
									"test": "is_family",
									"subject": "other",
									"value": "player"
								},
								{
									"test": "is_family",
									"subject": "other",
									"value": "snowgolem"
								},
								{
									"test": "is_family",
									"subject": "other",
									"value": "irongolem"
								}
							]
						},
						"max_dist": 35
					},
					{
						"filters": {
							"any_of": [
								{
									"test": "is_family",
									"subject": "other",
									"value": "villager"
								},
								{
									"test": "is_family",
									"subject": "other",
									"value": "wandering_trader"
								}
							]
						},
						"max_dist": 35,
						"must_see": false
					},
					{
						"filters": {
							"all_of": [
								{
									"test": "is_family",
									"subject": "other",
									"value": "baby_turtle"
								},
								{
									"test": "in_water",
									"subject": "other",
									"operator": "!=",
									"value": true
								}
							]
						},
						"max_dist": 35
					}
				]
			},
			"minecraft:physics": {},
			"minecraft:pushable": {
				"is_pushable": true,
				"is_pushable_by_piston": true
			},
			"minecraft:conditional_bandwidth_optimization": {},
			"minecraft:behavior.delayed_attack": {}
		},
		"events": {
			"minecraft:entity_spawned": {
				"randomize": [
					{
						"weight": 380,
						"remove": {},
						"add": {
							"component_groups": [
								"minecraft:zombie_adult",
								"minecraft:can_have_equipment"
							]
						}
					},
					{
						"weight": 17,
						"remove": {},
						"add": {
							"component_groups": [
								"minecraft:zombie_baby",
								"minecraft:can_have_equipment"
							]
						}
					},
					{
						"weight": 3,
						"remove": {},
						"add": {
							"component_groups": [
								"minecraft:zombie_baby",
								"minecraft:zombie_jockey",
								"minecraft:can_have_equipment"
							]
						}
					}
				]
			},
			"minecraft:as_adult": {
				"add": {
					"component_groups": [
						"minecraft:zombie_adult"
					]
				}
			},
			"minecraft:as_baby": {
				"add": {
					"component_groups": [
						"minecraft:zombie_baby"
					]
				}
			},
			"minecraft:start_transforming": {
				"add": {
					"component_groups": [
						"minecraft:start_drowned_transformation"
					]
				},
				"remove": {
					"component_groups": [
						"minecraft:look_to_start_drowned_transformation"
					]
				}
			},
			"minecraft:stop_transforming": {
				"add": {
					"component_groups": [
						"minecraft:look_to_start_drowned_transformation"
					]
				},
				"remove": {
					"component_groups": [
						"minecraft:start_drowned_transformation"
					]
				}
			},
			"minecraft:convert_to_drowned": {
				"sequence": [
					{
						"filters": {
							"test": "has_component",
							"operator": "!=",
							"value": "minecraft:is_baby"
						},
						"add": {
							"component_groups": [
								"minecraft:convert_to_drowned"
							]
						},
						"remove": {
							"component_groups": [
								"minecraft:start_drowned_transformation"
							]
						}
					},
					{
						"filters": {
							"test": "has_component",
							"value": "minecraft:is_baby"
						},
						"add": {
							"component_groups": [
								"minecraft:convert_to_baby_drowned"
							]
						},
						"remove": {
							"component_groups": [
								"minecraft:start_drowned_transformation"
							]
						}
					}
				]
			},
			"attack": {
				"queue_command": {
					"command": [
						"function attack"
					]
				}
			}
		}
	}
}