{"format_version": "1.16.100", "minecraft:entity": {"description": {"identifier": "oh:old_merchant", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"attentive": {"minecraft:movement": {"value": 0.4}, "minecraft:behavior.look_at_player": {"priority": 8, "look_distance": 8, "probability": 0.02}, "minecraft:timer": {"time": 30, "looping": false, "time_down_event": {"event": "desattentive", "target": "self"}}, "minecraft:is_saddled": {}}}, "components": {"minecraft:behavior.trade_interest": {"priority": 3, "within_radius": 6, "interest_time": 45, "remove_item_time": 1, "carried_item_switch_time": 2, "cooldown": 2}, "minecraft:economy_trade_table": {"display_name": "Old_Merchant", "table": "trading/economy_trades/old_merchant_trades.json", "new_screen": true}, "minecraft:behavior.look_at_trading_player": {"priority": 4}, "minecraft:behavior.trade_with_player": {"priority": 1}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}]}, "event": "attentive"}}]}, "minecraft:type_family": {"family": ["old_merchant"]}, "minecraft:mark_variant": {"value": 0}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:nameable": {}, "minecraft:movement": {"value": 0.0}, "minecraft:navigation.walk": {"can_path_over_water": true, "can_pass_doors": true, "can_open_doors": true, "avoid_water": true}, "minecraft:follow_range": {"value": 128}, "minecraft:annotation.open_door": {}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:persistent": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.avoid_mob_type": {"priority": 4, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_pigman"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "vex"}]}, "max_dist": 8, "walk_speed_multiplier": 0.6, "sprint_speed_multiplier": 0.6}]}, "minecraft:behavior.move_indoors": {"priority": 6, "speed_multiplier": 0.8, "timeout_cooldown": 8}, "minecraft:behavior.look_at_player": {"priority": 10, "look_distance": 0, "probability": 0.0}, "minecraft:behavior.move_towards_restriction": {"priority": 10, "speed_multiplier": 0.6}, "minecraft:behavior.random_stroll": {"priority": 10, "speed_multiplier": 0.6}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"desattentive": {"remove": {"component_groups": ["attentive"]}}, "attentive": {"add": {"component_groups": ["attentive"]}}, "become_witch": {"add": {"component_groups": ["become_witch"]}}, "become_zombie": {"sequence": [{"filters": {"test": "is_difficulty", "value": "normal"}, "randomize": [{"weight": 50, "add": {"component_groups": ["become_zombie"]}}, {"weight": 50}]}, {"filters": {"test": "is_difficulty", "value": "hard"}, "add": {"component_groups": ["become_zombie"]}}]}, "minecraft:entity_spawned": {"sequence": [{"filters": {"test": "has_component", "operator": "!=", "value": "minecraft:skin_id"}, "randomize": [{"weight": 1, "add": {"component_groups": ["villager_skin_0"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_1"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_2"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_3"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_4"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_5"]}}]}, {"filters": {"test": "has_component", "operator": "!=", "value": "minecraft:variant"}, "randomize": [{"weight": 5, "add": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 95, "sequence": [{"add": {"component_groups": ["adult"]}}, {"randomize": [{"weight": 1, "add": {"component_groups": ["farmer", "behavior_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["fisherman", "behavior_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["shepherd", "behavior_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["fletcher", "behavior_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["librarian", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["cartographer", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["cleric", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["armorer", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["weaponsmith", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["toolsmith", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["butcher", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["leatherworker", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["mason", "behavior_non_peasant", "basic_schedule"]}}, {"weight": 1, "add": {"component_groups": ["nitwit", "behavior_peasant", "jobless_schedule"]}}]}]}]}, {"filters": {"any_of": [{"test": "has_biome_tag", "value": "desert"}, {"test": "has_biome_tag", "value": "mesa"}]}, "add": {"component_groups": ["desert_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "jungle"}, "add": {"component_groups": ["jungle_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "savanna"}, "add": {"component_groups": ["savanna_villager"]}}, {"filters": {"any_of": [{"all_of": [{"test": "has_biome_tag", "value": "cold"}, {"test": "has_biome_tag", "operator": "!=", "value": "ocean"}]}, {"test": "has_biome_tag", "value": "frozen"}]}, "add": {"component_groups": ["snow_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "swamp"}, "add": {"component_groups": ["swamp_villager"]}}, {"filters": {"all_of": [{"any_of": [{"test": "has_biome_tag", "value": "taiga"}, {"test": "has_biome_tag", "value": "extreme_hills"}]}, {"test": "has_biome_tag", "operator": "!=", "value": "cold"}]}, "add": {"component_groups": ["taiga_villager"]}}]}, "minecraft:spawn_from_village": {"sequence": [{"filters": {"test": "has_component", "operator": "!=", "value": "minecraft:variant"}, "randomize": [{"weight": 5, "add": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 95, "add": {"component_groups": ["adult"]}, "sequence": [{"randomize": [{"weight": 90, "add": {"component_groups": ["unskilled", "behavior_peasant", "basic_schedule"]}}, {"weight": 10, "add": {"component_groups": ["nitwit", "behavior_peasant", "jobless_schedule"]}}]}]}]}, {"filters": {"test": "has_component", "operator": "!=", "value": "minecraft:skin_id"}, "randomize": [{"weight": 1, "add": {"component_groups": ["villager_skin_0"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_1"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_2"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_3"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_4"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_5"]}}]}, {"filters": {"any_of": [{"test": "has_biome_tag", "value": "desert"}, {"test": "has_biome_tag", "value": "mesa"}]}, "add": {"component_groups": ["desert_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "jungle"}, "add": {"component_groups": ["jungle_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "savanna"}, "add": {"component_groups": ["savanna_villager"]}}, {"filters": {"any_of": [{"all_of": [{"test": "has_biome_tag", "value": "cold"}, {"test": "has_biome_tag", "operator": "!=", "value": "ocean"}]}, {"test": "has_biome_tag", "value": "frozen"}]}, "add": {"component_groups": ["snow_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "swamp"}, "add": {"component_groups": ["swamp_villager"]}}, {"filters": {"all_of": [{"any_of": [{"test": "has_biome_tag", "value": "taiga"}, {"test": "has_biome_tag", "value": "extreme_hills"}]}, {"test": "has_biome_tag", "operator": "!=", "value": "cold"}]}, "add": {"component_groups": ["taiga_villager"]}}]}, "minecraft:entity_transformed": {"sequence": [{"filters": {"test": "has_component", "subject": "other", "operator": "==", "value": "minecraft:is_baby"}, "add": {"component_groups": ["baby", "child_schedule"]}}, {"filters": {"test": "has_component", "subject": "other", "operator": "!=", "value": "minecraft:is_baby"}, "add": {"component_groups": ["adult"]}, "sequence": [{"filters": {"test": "is_family", "subject": "other", "value": "farmer"}, "add": {"component_groups": ["farmer", "behavior_peasant", "farmer_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "fisherman"}, "add": {"component_groups": ["fisherman", "behavior_peasant", "fisher_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "shepherd"}, "add": {"component_groups": ["shepherd", "behavior_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "fletcher"}, "add": {"component_groups": ["fletcher", "behavior_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "librarian"}, "add": {"component_groups": ["librarian", "behavior_non_peasant", "librarian_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "cartographer"}, "add": {"component_groups": ["cartographer", "behavior_non_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "cleric"}, "add": {"component_groups": ["cleric", "behavior_non_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "armorer"}, "add": {"component_groups": ["armorer", "behavior_non_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "weaponsmith"}, "add": {"component_groups": ["weaponsmith", "behavior_non_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "toolsmith"}, "add": {"component_groups": ["toolsmith", "behavior_non_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "butcher"}, "add": {"component_groups": ["butcher", "behavior_non_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "leatherworker"}, "add": {"component_groups": ["leatherworker", "behavior_non_peasant", "work_schedule"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "mason"}, "add": {"component_groups": ["mason", "behavior_non_peasant", "work_schedule"]}}]}, {"filters": {"test": "is_family", "subject": "other", "operator": "==", "value": "zombie_villager"}, "sequence": [{"filters": {"test": "is_skin_id", "subject": "other", "value": 0}, "add": {"component_groups": ["villager_skin_0"]}}, {"filters": {"test": "is_skin_id", "subject": "other", "value": 1}, "add": {"component_groups": ["villager_skin_1"]}}, {"filters": {"test": "is_skin_id", "subject": "other", "value": 2}, "add": {"component_groups": ["villager_skin_2"]}}, {"filters": {"test": "is_skin_id", "subject": "other", "value": 3}, "add": {"component_groups": ["villager_skin_3"]}}, {"filters": {"test": "is_skin_id", "subject": "other", "value": 4}, "add": {"component_groups": ["villager_skin_4"]}}, {"filters": {"test": "is_skin_id", "subject": "other", "value": 5}, "add": {"component_groups": ["villager_skin_5"]}}, {"filters": {"test": "is_mark_variant", "subject": "other", "value": 1}, "add": {"component_groups": ["desert_villager"]}}, {"filters": {"test": "is_mark_variant", "subject": "other", "value": 2}, "add": {"component_groups": ["jungle_villager"]}}, {"filters": {"test": "is_mark_variant", "subject": "other", "value": 3}, "add": {"component_groups": ["savanna_villager"]}}, {"filters": {"test": "is_mark_variant", "subject": "other", "value": 4}, "add": {"component_groups": ["snow_villager"]}}, {"filters": {"test": "is_mark_variant", "subject": "other", "value": 5}, "add": {"component_groups": ["swamp_villager"]}}, {"filters": {"test": "is_mark_variant", "subject": "other", "value": 6}, "add": {"component_groups": ["taiga_villager"]}}]}, {"filters": {"test": "is_family", "subject": "other", "operator": "==", "value": "villager"}, "sequence": [{"randomize": [{"weight": 1, "add": {"component_groups": ["villager_skin_0"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_1"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_2"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_3"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_4"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_5"]}}]}, {"filters": {"any_of": [{"test": "has_biome_tag", "value": "desert"}, {"test": "has_biome_tag", "value": "mesa"}]}, "add": {"component_groups": ["desert_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "jungle"}, "add": {"component_groups": ["jungle_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "savanna"}, "add": {"component_groups": ["savanna_villager"]}}, {"filters": {"any_of": [{"all_of": [{"test": "has_biome_tag", "value": "cold"}, {"test": "has_biome_tag", "operator": "!=", "value": "ocean"}]}, {"test": "has_biome_tag", "value": "frozen"}]}, "add": {"component_groups": ["snow_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "swamp"}, "add": {"component_groups": ["swamp_villager"]}}, {"filters": {"all_of": [{"any_of": [{"test": "has_biome_tag", "value": "taiga"}, {"test": "has_biome_tag", "value": "extreme_hills"}]}, {"test": "has_biome_tag", "operator": "!=", "value": "cold"}]}, "add": {"component_groups": ["taiga_villager"]}}]}]}, "minecraft:entity_born": {"sequence": [{"filters": {"test": "has_component", "operator": "!=", "value": "minecraft:skin_id"}, "randomize": [{"weight": 1, "add": {"component_groups": ["villager_skin_0"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_1"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_2"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_3"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_4"]}}, {"weight": 1, "add": {"component_groups": ["villager_skin_5"]}}]}, {"add": {"component_groups": ["baby", "unskilled", "child_schedule"]}}, {"filters": {"test": "has_biome_tag", "value": "desert"}, "add": {"component_groups": ["desert_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "jungle"}, "add": {"component_groups": ["jungle_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "savanna"}, "add": {"component_groups": ["savanna_villager"]}}, {"filters": {"any_of": [{"test": "has_biome_tag", "value": "cold"}, {"test": "has_biome_tag", "value": "frozen"}]}, "add": {"component_groups": ["snow_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "swamp"}, "add": {"component_groups": ["swamp_villager"]}}, {"filters": {"test": "has_biome_tag", "value": "taiga"}, "add": {"component_groups": ["taiga_villager"]}}]}, "minecraft:spawn_farmer": {"randomize": [{"weight": 5, "add": {"component_groups": ["farmer", "adult", "behavior_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 5, "add": {"component_groups": ["fisherman", "adult", "behavior_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 5, "add": {"component_groups": ["shepherd", "adult", "behavior_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 5, "add": {"component_groups": ["fletcher", "adult", "behavior_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 5, "add": {"component_groups": ["mason", "adult", "behavior_non_peasant", "work_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}]}, "minecraft:spawn_librarian": {"randomize": [{"weight": 20, "add": {"component_groups": ["librarian", "adult", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 20, "add": {"component_groups": ["cartographer", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}]}, "minecraft:spawn_cleric": {"add": {"component_groups": ["cleric", "adult", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, "minecraft:spawn_armorer": {"randomize": [{"weight": 6, "add": {"component_groups": ["armorer", "adult", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 6, "add": {"component_groups": ["weaponsmith", "adult", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 6, "add": {"component_groups": ["toolsmith", "adult", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}]}, "minecraft:spawn_butcher": {"randomize": [{"weight": 10, "add": {"component_groups": ["butcher", "adult", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}, {"weight": 10, "add": {"component_groups": ["leatherworker", "adult", "behavior_non_peasant", "basic_schedule"]}, "remove": {"component_groups": ["baby", "child_schedule"]}}]}, "minecraft:ageable_grow_up": {"randomize": [{"weight": 10, "remove": {"component_groups": ["baby", "child_schedule"]}, "add": {"component_groups": ["adult", "nitwit", "behavior_peasant", "jobless_schedule"]}}, {"weight": 90, "remove": {"component_groups": ["baby", "child_schedule"]}, "add": {"component_groups": ["adult", "unskilled", "behavior_peasant", "basic_schedule"]}}]}}}}