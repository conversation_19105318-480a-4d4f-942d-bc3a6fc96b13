{"format_version": "1.16.100", "minecraft:entity": {"description": {"identifier": "oh:fairy_defender", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"died": {"minecraft:instant_despawn": {}}, "entity_timer": {"minecraft:timer": {"time": 60, "looping": false, "time_down_event": {"event": "died", "target": "self"}}}}, "components": {"minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 5 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:nameable": {}, "minecraft:type_family": {"family": ["fairy_defender"]}, "minecraft:equipment": {"table": "loot_tables/entities/fairy_defender_gear.json", "slot_drop_chance": [{"slot": "slot.weapon.mainhand", "drop_chance": 0.1}, {"slot": "slot.weapon.offhand", "drop_chance": 0.1}]}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:attack": {"damage": 1}, "minecraft:collision_box": {"width": 0.4, "height": 0.8}, "minecraft:despawn": {"despawn_from_distance": {"max_distance": 54, "min_distance": 40}}, "minecraft:behavior.random_hover": {"priority": 10, "xz_dist": 8, "y_dist": 8, "y_offset": -1, "interval": 1, "hover_height": [1, 4], "speed_multiplier": 0.4}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:movement": {"value": 0.3}, "minecraft:flying_speed": {"value": 0.15}, "minecraft:navigation.hover": {"can_path_over_water": true, "can_sink": false, "can_pass_doors": false, "can_path_from_air": true, "avoid_water": true, "avoid_damage_blocks": true, "avoid_sun": false}, "minecraft:movement.hover": {}, "minecraft:jump.static": {}, "minecraft:can_fly": {}, "minecraft:behavior.float": {"priority": 10}, "minecraft:behavior.melee_attack": {"priority": 0, "speed_multiplier": 1.2}, "minecraft:scale": {"value": 1.1}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "operator": "not", "value": "player"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "cow"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "chicken"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "pig"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "sheep"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "wolf"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "cat"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "fairy_defender"}, {"test": "is_family", "subject": "other", "operator": "not", "value": "inanimate"}]}, "max_dist": 20}], "must_see": true}, "minecraft:behavior.look_at_player": {"priority": 9, "look_distance": 6, "probability": 0.02}, "minecraft:behavior.look_at_entity": {"priority": 10, "look_distance": 6, "probability": 0.02, "filters": {"test": "is_family", "subject": "other", "value": "mob"}}, "minecraft:behavior.hurt_by_target": {"priority": 1}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 45}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["entity_timer"]}}, "died": {"run_command": {"command": ["particle minecraft:fairy_explode ~ ~0.5 ~"]}, "add": {"component_groups": ["died"]}}}}}