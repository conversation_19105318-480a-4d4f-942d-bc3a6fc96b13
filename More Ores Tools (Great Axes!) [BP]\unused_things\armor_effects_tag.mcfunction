execute if entity @a[hasitem={location=slot.armor.head,item=oh:topaz_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:topaz_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:topaz_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:topaz_boots}] run tag @s add set_topaz
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:topaz_helmet}] run tag @s remove set_topaz
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:topaz_chestplate}] run tag @s remove set_topaz
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:topaz_leggings}] run tag @s remove set_topaz
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:topaz_boots}] run tag @s remove set_topaz

execute if entity @a[hasitem={location=slot.armor.head,item=oh:tin_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:tin_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:tin_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:tin_boots}] run tag @s add set_tin
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:tin_helmet}] run tag @s remove set_tin
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:tin_chestplate}] run tag @s remove set_tin
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:tin_leggings}] run tag @s remove set_tin
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:tin_boots}] run tag @s remove set_tin

execute if entity @a[hasitem={location=slot.armor.head,item=oh:bronce_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:bronce_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:bronce_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:bronce_boots}] run tag @s add set_bronce
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:bronce_helmet}] run tag @s remove set_bronce
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:bronce_chestplate}] run tag @s remove set_bronce
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:bronce_leggings}] run tag @s remove set_bronce
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:bronce_boots}] run tag @s remove set_bronce

execute if entity @a[hasitem={location=slot.armor.head,item=oh:ruby_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:ruby_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:ruby_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:ruby_boots}] run tag @s add set_ruby
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:ruby_helmet}] run tag @s remove set_ruby
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:ruby_chestplate}] run tag @s remove set_ruby
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:ruby_leggings}] run tag @s remove set_ruby
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:ruby_boots}] run tag @s remove set_ruby

execute if entity @a[hasitem={location=slot.armor.head,item=oh:jade_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:jade_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:jade_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:jade_boots}] run tag @s add set_jade
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:jade_helmet}] run tag @s remove set_jade
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:jade_chestplate}] run tag @s remove set_jade
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:jade_leggings}] run tag @s remove set_jade
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:jade_boots}] run tag @s remove set_jade

execute if entity @a[hasitem={location=slot.armor.head,item=oh:migtinio_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:migtinio_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:migtinio_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:migtinio_boots}] run tag @s add set_migtinio
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:migtinio_helmet}] run tag @s remove set_migtinio
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:migtinio_chestplate}] run tag @s remove set_migtinio
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:migtinio_leggings}] run tag @s remove set_migtinio
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:migtinio_boots}] run tag @s remove set_migtinio

execute if entity @a[hasitem={location=slot.armor.head,item=oh:silver_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:silver_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:silver_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:silver_boots}] run tag @s add set_silver
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:silver_helmet}] run tag @s remove set_silver
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:silver_chestplate}] run tag @s remove set_silver
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:silver_leggings}] run tag @s remove set_silver
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:silver_boots}] run tag @s remove set_silver

execute if entity @a[hasitem={location=slot.armor.head,item=oh:orichalcum_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:orichalcum_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:orichalcum_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:orichalcum_boots}] run tag @s add set_orichalcum
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:orichalcum_helmet}] run tag @s remove set_orichalcum
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:orichalcum_chestplate}] run tag @s remove set_orichalcum
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:orichalcum_leggings}] run tag @s remove set_orichalcum
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:orichalcum_boots}] run tag @s remove set_orichalcum

execute if entity @a[hasitem={location=slot.armor.head,item=oh:titanium_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:titanium_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:titanium_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:titanium_boots}] run tag @s add set_titanium
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:titanium_helmet}] run tag @s remove set_titanium
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:titanium_chestplate}] run tag @s remove set_titanium
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:titanium_leggings}] run tag @s remove set_titanium
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:titanium_boots}] run tag @s remove set_titanium

execute if entity @a[hasitem={location=slot.armor.head,item=oh:platinum_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:platinum_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:platinum_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:platinum_boots}] run tag @s add set_platinum
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:platinum_helmet}] run tag @s remove set_platinum
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:platinum_chestplate}] run tag @s remove set_platinum
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:platinum_leggings}] run tag @s remove set_platinum
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:platinum_boots}] run tag @s remove set_platinum

execute if entity @a[hasitem={location=slot.armor.head,item=oh:mithril_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:mithril_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:mithril_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:mithril_boots}] run tag @s add set_mithril
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:mithril_helmet}] run tag @s remove set_mithril
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:mithril_chestplate}] run tag @s remove set_mithril
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:mithril_leggings}] run tag @s remove set_mithril
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:mithril_boots}] run tag @s remove set_mithril

execute if entity @a[hasitem={location=slot.armor.head,item=oh:paladium_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:paladium_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:paladium_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:paladium_boots}] run tag @s add set_paladium
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:paladium_helmet}] run tag @s remove set_paladium
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:paladium_chestplate}] run tag @s remove set_paladium
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:paladium_leggings}] run tag @s remove set_paladium
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:paladium_boots}] run tag @s remove set_paladium

execute if entity @a[hasitem={location=slot.armor.head,item=oh:tourmaline_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:tourmaline_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:tourmaline_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:tourmaline_boots}] run tag @s add set_tourmaline
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:tourmaline_helmet}] run tag @s remove set_tourmaline
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:tourmaline_chestplate}] run tag @s remove set_tourmaline
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:tourmaline_leggings}] run tag @s remove set_tourmaline
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:tourmaline_boots}] run tag @s remove set_tourmaline

execute if entity @a[hasitem={location=slot.armor.head,item=oh:onyx_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:onyx_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:onyx_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:onyx_boots}] run tag @s add set_onyx
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:onyx_helmet}] run tag @s remove set_onyx
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:onyx_chestplate}] run tag @s remove set_onyx
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:onyx_leggings}] run tag @s remove set_onyx
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:onyx_boots}] run tag @s remove set_onyx

execute if entity @a[hasitem={location=slot.armor.head,item=oh:aquarium_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:aquarium_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:aquarium_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:aquarium_boots}] run tag @s add set_aquarium
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:aquarium_helmet}] run tag @s remove set_aquarium
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:aquarium_chestplate}] run tag @s remove set_aquarium
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:aquarium_leggings}] run tag @s remove set_aquarium
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:aquarium_boots}] run tag @s remove set_aquarium

execute if entity @a[hasitem={location=slot.armor.head,item=oh:adamantite_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:adamantite_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:adamantite_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:adamantite_boots}] run tag @s add set_adamantite
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:adamantite_helmet}] run tag @s remove set_adamantite
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:adamantite_chestplate}] run tag @s remove set_adamantite
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:adamantite_leggings}] run tag @s remove set_adamantite
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:adamantite_boots}] run tag @s remove set_adamantite

execute if entity @a[hasitem={location=slot.armor.head,item=oh:cobalto_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:cobalto_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:cobalto_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:cobalto_boots}] run tag @s add set_cobalto
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:cobalto_helmet}] run tag @s remove set_cobalto
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:cobalto_chestplate}] run tag @s remove set_cobalto
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:cobalto_leggings}] run tag @s remove set_cobalto
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:cobalto_boots}] run tag @s remove set_cobalto

execute if entity @a[hasitem={location=slot.armor.head,item=oh:enderite_helmet}] if entity @a[hasitem={location=slot.armor.chest,item=oh:enderite_chestplate}] if entity @a[hasitem={location=slot.armor.legs,item=oh:enderite_leggings}] if entity @a[hasitem={location=slot.armor.feet,item=oh:enderite_boots}] run tag @s add set_enderite
execute unless entity @a[hasitem={location=slot.armor.head,item=oh:enderite_helmet}] run tag @s remove set_enderite
execute unless entity @a[hasitem={location=slot.armor.chest,item=oh:enderite_chestplate}] run tag @s remove set_enderite
execute unless entity @a[hasitem={location=slot.armor.legs,item=oh:enderite_leggings}] run tag @s remove set_enderite
execute unless entity @a[hasitem={location=slot.armor.feet,item=oh:enderite_boots}] run tag @s remove set_enderite