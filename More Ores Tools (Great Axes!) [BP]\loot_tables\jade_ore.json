{
  "pools": [
    {
      "rolls": 1,
      "conditions": [
        {
          "condition": "match_tool",
          "item": [
            "oh:tin_pickaxe",
            "oh:dramantite_pickaxe",
            "oh:aquarium_pickaxe",
            "oh:topaz_pickaxe",
            "oh:emerald_pickaxe",
            "oh:copper_pickaxe",
            "oh:amethyst_pickaxe",
            "oh:tourmaline_pickaxe",
            "oh:titanium_pickaxe",
            "oh:ruby_pickaxe",
            "oh:platinum_pickaxe",
            "oh:paladium_pickaxe",
            "oh:orichalcum_pickaxe",
            "oh:onyx_pickaxe",
            "oh:mithril_pickaxe",
            "oh:migtinio_pickaxe",
            "oh:jade_pickaxe",
            "oh:silver_pickaxe",
            "oh:enderite_pickaxe",
            "oh:cobalto_pickaxe",
            "oh:bronce_pickaxe",
            "oh:adamantite_pickaxe"
          ]
        }
      ],
      "entries": [
        {
          "type": "item",
          "name": "oh:jade"
        }
      ]
    },
    //---- <PERSON>qui para Fortuna I ----
    {
      "rolls": 1,
      "conditions": [
        {
          "condition": "match_tool",
          "item": [
            "oh:tin_pickaxe",
            "oh:dramantite_pickaxe",
            "oh:aquarium_pickaxe",
            "oh:topaz_pickaxe",
            "oh:emerald_pickaxe",
            "oh:copper_pickaxe",
            "oh:amethyst_pickaxe",
            "oh:tourmaline_pickaxe",
            "oh:titanium_pickaxe",
            "oh:ruby_pickaxe",
            "oh:platinum_pickaxe",
            "oh:paladium_pickaxe",
            "oh:orichalcum_pickaxe",
            "oh:onyx_pickaxe",
            "oh:mithril_pickaxe",
            "oh:migtinio_pickaxe",
            "oh:jade_pickaxe",
            "oh:silver_pickaxe",
            "oh:enderite_pickaxe",
            "oh:cobalto_pickaxe",
            "oh:bronce_pickaxe",
            "oh:adamantite_pickaxe"
          ],
          "enchantments": [
            {
              "enchantment": "fortune",
              "levels": {
                "range_min": 1,
                "range_max": 1
              }
            }
          ]
        }
      ],
      "entries": [
        {
          "type": "item",
          "name": "oh:jade",
          "weight": 1,
          "functions": [
            {
              "function": "set_count",
              "count": {
                "min": 1,
                "max": 1
              }
            }
          ]
        },
        {
          "type": "empty",
          "weight": 2
        }
      ]
    },
    //---- Aqui para Fortuna II ----
    {
      "rolls": 1,
      "conditions": [
        {
          "condition": "match_tool",
          "item": [
            "oh:tin_pickaxe",
            "oh:dramantite_pickaxe",
            "oh:aquarium_pickaxe",
            "oh:topaz_pickaxe",
            "oh:emerald_pickaxe",
            "oh:copper_pickaxe",
            "oh:amethyst_pickaxe",
            "oh:tourmaline_pickaxe",
            "oh:titanium_pickaxe",
            "oh:ruby_pickaxe",
            "oh:platinum_pickaxe",
            "oh:paladium_pickaxe",
            "oh:orichalcum_pickaxe",
            "oh:onyx_pickaxe",
            "oh:mithril_pickaxe",
            "oh:migtinio_pickaxe",
            "oh:jade_pickaxe",
            "oh:silver_pickaxe",
            "oh:enderite_pickaxe",
            "oh:cobalto_pickaxe",
            "oh:bronce_pickaxe",
            "oh:adamantite_pickaxe"
          ],
          "enchantments": [
            {
              "enchantment": "fortune",
              "levels": {
                "range_min": 2,
                "range_max": 2
              }
            }
          ]
        }
      ],
      "entries": [
        {
          "type": "item",
          "name": "oh:jade",
          "weight": 1,
          "functions": [
            {
              "function": "set_count",
              "count": {
                "min": 2,
                "max": 2
              }
            }
          ]
        },
        {
          "type": "item",
          "name": "oh:jade",
          "weight": 1,
          "functions": [
            {
              "function": "set_count",
              "count": {
                "min": 2,
                "max": 3
              }
            }
          ]
        },
        {
          "type": "empty",
          "weight": 2
        }
      ]
    },
    //---- Aqui para Fortuna III ----
    {
      "rolls": 1,
      "conditions": [
        {
          "condition": "match_tool",
          "item": [
            "oh:tin_pickaxe",
            "oh:dramantite_pickaxe",
            "oh:aquarium_pickaxe",
            "oh:topaz_pickaxe",
            "oh:emerald_pickaxe",
            "oh:copper_pickaxe",
            "oh:amethyst_pickaxe",
            "oh:tourmaline_pickaxe",
            "oh:titanium_pickaxe",
            "oh:ruby_pickaxe",
            "oh:platinum_pickaxe",
            "oh:paladium_pickaxe",
            "oh:orichalcum_pickaxe",
            "oh:onyx_pickaxe",
            "oh:mithril_pickaxe",
            "oh:migtinio_pickaxe",
            "oh:jade_pickaxe",
            "oh:silver_pickaxe",
            "oh:enderite_pickaxe",
            "oh:cobalto_pickaxe",
            "oh:bronce_pickaxe",
            "oh:adamantite_pickaxe"
          ],
          "enchantments": [
            {
              "enchantment": "fortune",
              "levels": {
                "range_min": 3,
                "range_max": 3
              }
            }
          ]
        }
      ],
      "entries": [
        {
          "type": "item",
          "name": "oh:jade",
          "weight": 1,
          "functions": [
            {
              "function": "set_count",
              "count": {
                "min": 2,
                "max": 2
              }
            }
          ]
        },
        {
          "type": "item",
          "name": "oh:jade",
          "weight": 1,
          "functions": [
            {
              "function": "set_count",
              "count": {
                "min": 3,
                "max": 3
              }
            }
          ]
        },
        {
          "type": "empty",
          "weight": 3
        }
      ]
    }
  ]
}