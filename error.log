Dedicated_Server.txt
[2025-09-05 12:04:38:397 INFO] Starting Server
[2025-09-05 12:04:38:397 INFO] Version: 1.21.102.1
[2025-09-05 12:04:38:397 INFO] Session ID: 29315ca9-83a1-46f1-a8e0-d32a150f8eac
[2025-09-05 12:04:38:397 INFO] Build ID: 36160546
[2025-09-05 12:04:38:397 INFO] Branch: r/21_u10
[2025-09-05 12:04:38:397 INFO] Commit ID: 468d0e14b95e8c003f79d1a82cdaa729522efab6
[2025-09-05 12:04:38:397 INFO] Configuration: Publish
[2025-09-05 12:04:38:397 INFO] Level Name: Bedrock level
[2025-09-05 12:04:38:400 INFO] No CDN config file found for dedicated server
[2025-09-05 12:04:38:400 INFO] Game mode: 0 Survival
[2025-09-05 12:04:38:400 INFO] Difficulty: 3 HARD
[2025-09-05 12:04:38:401 INFO] Content logging to console is enabled.
[2025-09-05 12:04:38:779 ERROR] [Addon] Cannot determine which pack manifest to use: Multiple manifests found at the same directory level in the pack's folder hierarchy.

[2025-09-05 12:04:39:433 ERROR] The following issues were found when loading packs:
	
		Unable to find manifest in pack.
[2025-09-05 12:04:39:433 INFO] Experiment(s) active: biom, ucft, gtst, exca, vtra, ddjs
[2025-09-05 12:04:39:434 INFO] Opening level 'worlds/Bedrock level/db'
[2025-09-05 12:04:39:564 INFO] [SERVER] Pack Stack - [00][Farmer's Delight[BP]] [packid_ver][d0131071-9ec1-edcd-6871-857c700703d8_4.1.0] [path][behavior_packs/[BP] FarmersDelightV4.1.0-1.21.100+]
[2025-09-05 12:04:39:564 INFO] [SERVER] Pack Stack - [01][Advanced Waypoints v6 (Addon)] [packid_ver][ee9ec48e-985b-4b1b-9c78-f533de55fbf7_6.0.0] [path][behavior_packs/Advanced Waypoint (Addon)]
[2025-09-05 12:04:39:564 INFO] [SERVER] Pack Stack - [02][§dSilk Touch Spawners §r] [packid_ver][e0e76179-a840-41ef-a0fa-8f8cb1ffd402_1.0.16] [path][behavior_packs/[BP] [1.21.100+] SilkTouchSpawners (v1.0.16)]
[2025-09-05 12:04:39:564 INFO] [SERVER] Pack Stack - [03][Raiyon's Dynamic Lightning[§cV3.2.7§r] 1.21.90+] [packid_ver][657087d5-3a90-4ea6-b7dc-10ae07e31ce5_3.2.7] [path][behavior_packs/Dynamic_LightningV3.2.7_Beharviors_Packs]
[2025-09-05 12:04:39:564 INFO] [SERVER] Pack Stack - [04][More Ores Tools (Great Axes!) [BP]] [packid_ver][cbd26b54-043d-41c9-8cf8-423c0b9d556e_2.0.0] [path][behavior_packs/More Ores Tools (Great Axes!) [BP]]
[2025-09-05 12:04:39:564 INFO] [SERVER] Pack Stack - [05][Clearlag System] [packid_ver][771b792e-ef67-4edf-8fb7-885e67a00957_1.16.10] [path][behavior_packs/Clearlag-v2.0.1-hotfix]
[2025-09-05 12:04:40:142 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore.json | oh:cobalto_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:142 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore.json | oh:cobalto_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:143 ERROR] [Components] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/barrel_with_gold.block.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/barrel_with_gold.block.json | oh:barrel_with_gold | components | To use component 'minecraft:selection_box', use json format version 1.19.60 or higher

[2025-09-05 12:04:40:143 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/barrel_with_gold.block.json | Unexpected version for the loaded data

[2025-09-05 12:04:40:143 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tin_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tin_ore_d.json | oh:tin_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:143 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tin_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tin_ore_d.json | oh:tin_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:143 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/light_b.json | Unexpected version for the loaded data

[2025-09-05 12:04:40:144 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore.json | oh:jade_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:144 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore.json | oh:jade_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:144 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/enderite_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/enderite_ore.json | oh:enderite_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:144 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/enderite_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/enderite_ore.json | oh:enderite_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:144 ERROR] [Components] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/magma_zombie_spawn.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/magma_zombie_spawn.json | oh:spawn_magma_zombie | components | To use component 'minecraft:material_instances', use json format version 1.19.40 or higher

[2025-09-05 12:04:40:144 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/magma_zombie_spawn.json | Unexpected version for the loaded data

[2025-09-05 12:04:40:144 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore.json | oh:tourmaline_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:144 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore.json | oh:tourmaline_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:145 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_d.json | oh:orichalcum_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:145 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_d.json | oh:orichalcum_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:145 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore_d.json | oh:cobalto_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:145 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/cobalto_ore_d.json | oh:cobalto_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:145 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore.json | oh:orichalcum_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:145 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore.json | oh:orichalcum_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:146 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore.json | oh:mithril_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:146 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore.json | oh:mithril_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:146 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore.json | oh:adamantite_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:146 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore.json | oh:adamantite_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:146 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore_d.json | oh:adamantite_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:146 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/adamantite_ore_d.json | oh:adamantite_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:147 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/topaz_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/topaz_ore_d.json | oh:topaz_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:147 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/topaz_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/topaz_ore_d.json | oh:topaz_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:148 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore_d.json | oh:silver_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:148 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore_d.json | oh:silver_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:148 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_d.json | oh:onyx_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:148 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_d.json | oh:onyx_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:148 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore.json | oh:aquarium_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:148 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore.json | oh:aquarium_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:149 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore_d.json | oh:mithril_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:149 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/mithril_ore_d.json | oh:mithril_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:149 ERROR] [Components] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/barrel_without_gold.block.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/barrel_without_gold.block.json | oh:barrel_without_gold | components | To use component 'minecraft:selection_box', use json format version 1.19.60 or higher

[2025-09-05 12:04:40:149 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/barrel_without_gold.block.json | Unexpected version for the loaded data

[2025-09-05 12:04:40:149 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore_d.json | oh:ruby_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:149 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore_d.json | oh:ruby_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:149 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore_d.json | oh:migtinio_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:149 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore_d.json | oh:migtinio_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:150 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_d.json | oh:dramantite_ore_d | events | xp | run_command | {
   "command" : [
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~"
   ],
   "target" : "self"
}


[2025-09-05 12:04:40:150 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_d.json | oh:dramantite_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:150 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore.json | oh:silver_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:150 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/silver_ore.json | oh:silver_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:150 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/paladium_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/paladium_ore_d.json | oh:paladium_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:150 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/paladium_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/paladium_ore_d.json | oh:paladium_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:150 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_n.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_n.json | oh:dramantite_ore_n | events | xp | run_command | {
   "command" : [
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~",
      "summon xp_orb ~ ~ ~"
   ],
   "target" : "self"
}


[2025-09-05 12:04:40:150 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_n.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/dramantite_ore_n.json | oh:dramantite_ore_n | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:151 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore.json | oh:ruby_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:151 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/ruby_ore.json | oh:ruby_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:152 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore_d.json | oh:jade_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:152 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/jade_ore_d.json | oh:jade_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:152 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_n.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_n.json | oh:orichalcum_ore_n | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:152 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_n.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/orichalcum_ore_n.json | oh:orichalcum_ore_n | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:152 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore_d.json | oh:aquarium_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:152 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/aquarium_ore_d.json | oh:aquarium_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:153 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore.json | oh:onyx_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:153 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore.json | oh:onyx_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:154 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_n.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_n.json | oh:onyx_ore_n | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:154 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_n.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/onyx_ore_n.json | oh:onyx_ore_n | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:154 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore.json | oh:migtinio_ore | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:154 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/migtinio_ore.json | oh:migtinio_ore | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:154 INFO] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore_d.json | oh:tourmaline_ore_d | events | xp | run_command | {
   "command" : [ "summon xp_orb ~ ~ ~", "summon xp_orb ~ ~ ~" ],
   "target" : "self"
}


[2025-09-05 12:04:40:154 ERROR] [Blocks] block_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore_d.json | behavior_packs/More Ores Tools (Great Axes!) [BP] | blocks/tourmaline_ore_d.json | oh:tourmaline_ore_d | events | xp | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:542 ERROR] [Item] To use item 'oh:copper_head', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:543 ERROR] [Item] To use item 'oh:adamantite_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:544 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "target" : "item",
   "type" : "durability"
}


[2025-09-05 12:04:40:544 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:544 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:544 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:544 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:544 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:544 WARN] [Item] oh:adamantite_shovel -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:547 ERROR] [Item] To use item 'oh:adamantite_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:550 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:550 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:550 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:550 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:550 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:550 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:550 WARN] [Item] oh:adamantite_hoe -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:554 ERROR] [Item] To use item 'oh:Adamantite_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:554 ERROR] [Item] To use item 'oh:cobalto_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:557 ERROR] [Item] To use item 'oh:cobalto_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:564 ERROR] [Item] To use item 'oh:cobalto_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:571 ERROR] [Item] To use item 'oh:emerald_head', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:576 ERROR] [Item] To use item 'oh:tin_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:576 ERROR] [Item] To use item 'oh:tin_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:577 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "target" : "self",
   "type" : "durability"
}


[2025-09-05 12:04:40:577 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:577 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:577 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:577 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:577 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:577 WARN] [Item] oh:tin_hoe -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:578 ERROR] [Item] To use item 'oh:tin_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:583 ERROR] [Item] To use item 'oh:mithril_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:590 ERROR] [Item] To use item 'oh:mithril_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:592 ERROR] [Item] To use item 'oh:mithril_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:594 ERROR] [Item] To use item 'oh:topaz_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:598 ERROR] [Item] To use item 'oh:topaz_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:602 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "target" : "self",
   "type" : "durability"
}


[2025-09-05 12:04:40:602 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:602 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:602 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:602 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:602 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:602 WARN] [Item] oh:topaz_hoe -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:603 ERROR] [Item] To use item 'oh:topaz_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:604 ERROR] [Item] To use item 'oh:orichalcum_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:610 ERROR] [Item] To use item 'oh:orichalcum_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:615 ERROR] [Item] To use item 'oh:orichalcum_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:627 ERROR] [Item] To use item 'oh:tourmaline_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:627 ERROR] [Item] To use item 'oh:tourmaline_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:628 ERROR] [Item] To use item 'oh:tourmaline_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:638 ERROR] [Item] To use item 'oh:aquarium_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:646 ERROR] [Item] To use item 'oh:aquarium_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:646 ERROR] [Item] To use item 'oh:aquarium_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:651 ERROR] [Item] To use item 'oh:silver_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:651 ERROR] [Item] To use item 'oh:silver_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:651 ERROR] [Item] To use item 'oh:silver_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:657 ERROR] [Item] minecraft:on_use has been deprecated and is not available in json format 1.20.80

[2025-09-05 12:04:40:658 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:658 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:658 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:658 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:658 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:658 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:658 INFO] [Item] salto | add_mob_effect | {
   "amplifier" : 1,
   "duration" : 20,
   "effect" : "strength"
}


[2025-09-05 12:04:40:658 ERROR] [Item] salto | add_mob_effect | child 'add_mob_effect' not valid here.

[2025-09-05 12:04:40:658 INFO] [Item] salto | damage | {
   "amount" : 100,
   "target" : "self",
   "type" : "none"
}


[2025-09-05 12:04:40:658 ERROR] [Item] salto | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:658 WARN] [Item] oh:silver_sword -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:666 ERROR] [Item] To use item 'oh:ruby_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:667 ERROR] [Item] To use item 'oh:ruby_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:669 ERROR] [Item] To use item 'oh:ruby_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:670 ERROR] [Item] To use item 'oh:onyx_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:670 ERROR] [Item] To use item 'oh:onyx_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:677 ERROR] [Item] To use item 'oh:onyx_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:680 ERROR] [Item] To use item 'oh:dramantite_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:681 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:681 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:681 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:681 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:681 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:681 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:681 WARN] [Item] oh:dramantite_hoe -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:684 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "target" : "item",
   "type" : "durability"
}


[2025-09-05 12:04:40:684 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:684 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:684 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:684 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:684 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:684 WARN] [Item] oh:dramantite_shovel -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:689 ERROR] [Item] To use item 'oh:dramantite_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:691 ERROR] [Item] To use item 'oh:dramantite_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:694 ERROR] [Item] To use item 'oh:titanium_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:695 ERROR] [Item] To use item 'oh:titanium_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:703 ERROR] [Item] To use item 'oh:titanium_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:704 ERROR] [Item] minecraft:on_use has been deprecated and is not available in json format 1.20.80

[2025-09-05 12:04:40:704 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:704 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:704 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:705 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:705 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:705 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:705 INFO] [Item] salto | add_mob_effect | {
   "amplifier" : 1,
   "duration" : 20,
   "effect" : "strength"
}


[2025-09-05 12:04:40:705 ERROR] [Item] salto | add_mob_effect | child 'add_mob_effect' not valid here.

[2025-09-05 12:04:40:705 INFO] [Item] salto | damage | {
   "amount" : 100,
   "target" : "self",
   "type" : "none"
}


[2025-09-05 12:04:40:705 ERROR] [Item] salto | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:705 WARN] [Item] oh:migtinio_sword -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:711 ERROR] [Item] To use item 'oh:migtinio_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:711 ERROR] [Item] To use item 'oh:migtinio_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:715 ERROR] [Item] To use item 'oh:migtinio_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:719 ERROR] [Item] To use item 'oh:amethyst_head', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:721 WARN] [Item] oh:amethyst_hammer -> components -> minecraft:digger -> destroy_speeds[0] -> on_dig: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:721 WARN] [Item] oh:amethyst_hammer -> components -> minecraft:digger -> destroy_speeds[4] -> on_dig: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:721 WARN] [Item] oh:amethyst_hammer -> components -> minecraft:digger -> on_dig: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:724 ERROR] [Item] To use item 'oh:enderite_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:732 ERROR] [Item] To use item 'oh:enderite_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:732 ERROR] [Item] To use item 'oh:enderite_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:743 ERROR] [Item] To use item 'oh:platinum_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:745 ERROR] [Item] To use item 'oh:platinum_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:745 ERROR] [Item] To use item 'oh:platinum_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:749 ERROR] [Item] To use item 'oh:jade_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:749 ERROR] [Item] To use item 'oh:jade_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:753 ERROR] [Item] minecraft:on_use has been deprecated and is not available in json format 1.20.80

[2025-09-05 12:04:40:754 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:754 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:754 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:754 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:754 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:754 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:754 INFO] [Item] salto | add_mob_effect | {
   "amplifier" : 1,
   "duration" : 10,
   "effect" : "speed"
}


[2025-09-05 12:04:40:754 ERROR] [Item] salto | add_mob_effect | child 'add_mob_effect' not valid here.

[2025-09-05 12:04:40:754 INFO] [Item] salto | damage | {
   "amount" : 100,
   "target" : "self",
   "type" : "none"
}


[2025-09-05 12:04:40:754 ERROR] [Item] salto | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:754 WARN] [Item] oh:jade_sword -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:754 ERROR] [Item] To use item 'oh:jade_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:758 ERROR] [Item] To use item 'oh:paladium_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:762 ERROR] [Item] To use item 'oh:paladium_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:765 ERROR] [Item] To use item 'oh:paladium_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:769 ERROR] [Item] To use item 'oh:bronce_battleaxe', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:769 ERROR] [Item] To use item 'oh:bronce_apple', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:769 ERROR] [Item] To use item 'oh:bronce_dagger', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:773 INFO] [Item] dmg | damage | {
   "amount" : 1,
   "target" : "self",
   "type" : "durability"
}


[2025-09-05 12:04:40:773 ERROR] [Item] dmg | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:773 INFO] [Item] on_tool_used | damage | {
   "amount" : 1,
   "type" : "durability"
}


[2025-09-05 12:04:40:773 ERROR] [Item] on_tool_used | damage | child 'damage' not valid here.

[2025-09-05 12:04:40:773 INFO] [Item] on_tool_used | swing | {}


[2025-09-05 12:04:40:773 ERROR] [Item] on_tool_used | swing | child 'swing' not valid here.

[2025-09-05 12:04:40:773 WARN] [Item] oh:bronce_hoe -> events: this member was found in the input, but is not present in the Schema

[2025-09-05 12:04:40:773 ERROR] [Item] To use item 'oh:bronce_head', use json format version 1.20.0 or higher

[2025-09-05 12:04:40:832 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:magma_zombie | minecraft:entity | events | attack | run_command | {
   "command" : [ "function attack" ]
}


[2025-09-05 12:04:40:832 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:magma_zombie | minecraft:entity | events | attack | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:834 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:fairy_defender | minecraft:entity | events | died | run_command | {
   "command" : [ "particle minecraft:fairy_explode ~ ~0.5 ~" ]
}


[2025-09-05 12:04:40:834 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:fairy_defender | minecraft:entity | events | died | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:843 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | event_particle | run_command | {
   "command" : [ "particle oh:magic_aura_particle ~ ~0.5 ~" ]
}


[2025-09-05 12:04:40:843 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | event_particle | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:843 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | deffend | run_command | {
   "command" : [
      "playanimation @e[c=1,r=1] animation.old_merchant_hurt",
      "playsound deffend.sound @p ~ ~ ~ 1 0.8",
      "summon oh:fairy ~-0.2 ~1 ~",
      "summon oh:fairy ~0.2 ~1 ~",
      "summon oh:fairy ~ ~1 ~"
   ]
}


[2025-09-05 12:04:40:843 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | deffend | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:843 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | block | run_command | {
   "command" : [ "playsound shield.block @p ~ ~ ~ 1 1" ]
}


[2025-09-05 12:04:40:843 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | block | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:843 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | block | run_command | {
   "command" : [ "playsound shield.block @p ~ ~ ~ 1 1.1" ]
}


[2025-09-05 12:04:40:843 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | block | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:843 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | block | run_command | {
   "command" : [ "playsound shield.block @p ~ ~ ~ 1 1.2" ]
}


[2025-09-05 12:04:40:843 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | block | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:843 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | animation_x | run_command | {
   "command" : [ "playanimation @e[c=1,r=1] animation.old_merchant_hurt" ]
}


[2025-09-05 12:04:40:843 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:old_merchant | minecraft:entity | events | animation_x | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:40:845 INFO] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:fairy | minecraft:entity | events | died | run_command | {
   "command" : [ "particle minecraft:fairy_explode ~ ~0.5 ~" ]
}


[2025-09-05 12:04:40:845 ERROR] [Actor] Bedrock level | actor_definitions | behavior_packs/More Ores Tools (Great Axes!) [BP] | oh:fairy | minecraft:entity | events | died | run_command | child 'run_command' not valid here.

[2025-09-05 12:04:41:548 ERROR] [FeatureRegistry] Bedrock level | oh:mini_dungeon_feature | minecraft:structure_template_feature | structure_name | Structure mystructure:mini_dungeon does not exist

[2025-09-05 12:04:41:551 ERROR] [FeatureRegistry] Bedrock level | minecraft:ores_house_feature | minecraft:structure_template_feature | structure_name | Structure mystructure:ores_house does not exist

[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_boots.json | oh:copper_boots | The Item: oh:copper_boots is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_boots.json | oh:copper_boots | Recipe result malformed


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_leggings.json | oh:copper_leggings | The Item: oh:copper_leggings is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_leggings.json | oh:copper_leggings | Recipe result malformed


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_hoe.json | oh:copper_hoe | The Item: oh:copper_hoe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_hoe.json | oh:copper_hoe | Recipe result malformed


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_hoe.json | oh:copper_hoe | Recipe has no result item(s)


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_shovel.json | oh:copper_shovel | The Item: oh:copper_shovel is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:713 ERROR] [Recipes] recipes/copper/copper_shovel.json | oh:copper_shovel | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_shovel.json | oh:copper_shovel | Recipe has no result item(s)


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_pickaxe.json | oh:copper_pickaxe | The Item: oh:copper_pickaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_pickaxe.json | oh:copper_pickaxe | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_pickaxe.json | oh:copper_pickaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_sword.json | oh:copper_sword | The Item: oh:copper_sword is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_sword.json | oh:copper_sword | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_sword.json | oh:copper_sword | Recipe has no result item(s)


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_axe.json | oh:copper_axe | The Item: oh:copper_axe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_axe.json | oh:copper_axe | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_axe.json | oh:copper_axe | Recipe has no result item(s)


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_chestplate.json | oh:copper_chestplate | The Item: oh:copper_chestplate is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_chestplate.json | oh:copper_chestplate | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_head.json | oh:copper_head | The Item: oh:copper_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_head.json | oh:copper_head | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_helmet.json | oh:copper_helmet | The Item: oh:copper_helmet is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_helmet.json | oh:copper_helmet | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_hammer.json | oh:copper_hammer | The Item: oh:copper_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/copper/copper_hammer.json | oh:copper_hammer | Recipe for: oh:copper_hammer is missing (unknown) ingredient


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/adamantite/adamantite_battleaxe.json | oh:adamantite_battleaxe | The Item: oh:adamantite_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/adamantite/adamantite_battleaxe.json | oh:adamantite_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/adamantite/adamantite_battleaxe.json | oh:adamantite_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:714 ERROR] [Recipes] recipes/adamantite/adamantite_block.json | Missing version tag


[2025-09-05 12:04:41:715 ERROR] [Recipes] recipes/adamantite/adamantite_boots.json | Missing version tag


[2025-09-05 12:04:41:715 ERROR] [Recipes] recipes/adamantite/adamantite_chestplate.json | Missing version tag


[2025-09-05 12:04:41:715 ERROR] [Recipes] recipes/adamantite/adamantite_dagger.json | oh:adamantite_dagger | The Item: oh:adamantite_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:715 ERROR] [Recipes] recipes/adamantite/adamantite_dagger.json | oh:adamantite_dagger | Recipe result malformed


[2025-09-05 12:04:41:715 ERROR] [Recipes] recipes/adamantite/adamantite_dagger.json | oh:adamantite_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/adamantite/adamantite_2.json | Missing version tag


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/adamantite/adamantite_leggings.json | Missing version tag


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/adamantite/adamantite_head.json | Missing version tag


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/adamantite/adamantite_helmet.json | Missing version tag


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/adamantite/adamantite_apple.json | Missing version tag


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/adamantite/adamantite_apple.json | oh:adamantite_apple | The Item: oh:adamantite_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/adamantite/adamantite_apple.json | oh:adamantite_apple | Recipe result malformed


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/cobalt/cobalto_dagger.json | oh:cobalto_dagger | The Item: oh:cobalto_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/cobalt/cobalto_dagger.json | oh:cobalto_dagger | Recipe result malformed


[2025-09-05 12:04:41:716 ERROR] [Recipes] recipes/cobalt/cobalto_dagger.json | oh:cobalto_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:717 ERROR] [Recipes] recipes/cobalt/cobalto_2.json | Missing version tag


[2025-09-05 12:04:41:717 ERROR] [Recipes] recipes/cobalt/cobalto_helmet.json | Missing version tag


[2025-09-05 12:04:41:717 ERROR] [Recipes] recipes/cobalt/cobalto_apple.json | Missing version tag


[2025-09-05 12:04:41:717 ERROR] [Recipes] recipes/cobalt/cobalto_apple.json | oh:cobalto_apple | The Item: oh:cobalto_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:717 ERROR] [Recipes] recipes/cobalt/cobalto_apple.json | oh:cobalto_apple | Recipe result malformed


[2025-09-05 12:04:41:717 ERROR] [Recipes] recipes/cobalt/cobalto_boots.json | Missing version tag


[2025-09-05 12:04:41:717 ERROR] [Recipes] recipes/cobalt/cobalto_leggings.json | Missing version tag


[2025-09-05 12:04:41:718 ERROR] [Recipes] recipes/cobalt/cobalto_head.json | Missing version tag


[2025-09-05 12:04:41:718 ERROR] [Recipes] recipes/cobalt/cobalto_block.json | Missing version tag


[2025-09-05 12:04:41:718 ERROR] [Recipes] recipes/cobalt/cobalto_battleaxe.json | oh:cobalto_battleaxe | The Item: oh:cobalto_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:718 ERROR] [Recipes] recipes/cobalt/cobalto_battleaxe.json | oh:cobalto_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:718 ERROR] [Recipes] recipes/cobalt/cobalto_battleaxe.json | oh:cobalto_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:718 ERROR] [Recipes] recipes/cobalt/cobalto_chestplate.json | Missing version tag


[2025-09-05 12:04:41:719 ERROR] [Recipes] recipes/emerald/emerald_hammer.json | oh:emerald_hammer | The Item: oh:emerald_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:719 ERROR] [Recipes] recipes/emerald/emerald_hammer.json | oh:emerald_hammer | Recipe for: oh:emerald_hammer is missing (unknown) ingredient


[2025-09-05 12:04:41:719 ERROR] [Recipes] recipes/emerald/emerald_head.json | oh:emerald_head | The Item: oh:emerald_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:719 ERROR] [Recipes] recipes/emerald/emerald_head.json | oh:emerald_head | Recipe result malformed


[2025-09-05 12:04:41:720 ERROR] [Recipes] recipes/tin/tin_block.json | Missing version tag


[2025-09-05 12:04:41:720 ERROR] [Recipes] recipes/tin/tin_boots.json | Missing version tag


[2025-09-05 12:04:41:720 ERROR] [Recipes] recipes/tin/tin_apple.json | Missing version tag


[2025-09-05 12:04:41:720 ERROR] [Recipes] recipes/tin/tin_apple.json | oh:tin_apple | The Item: oh:tin_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:720 ERROR] [Recipes] recipes/tin/tin_apple.json | oh:tin_apple | Recipe result malformed


[2025-09-05 12:04:41:720 ERROR] [Recipes] recipes/tin/tin_2.json | Missing version tag


[2025-09-05 12:04:41:721 ERROR] [Recipes] recipes/tin/tin_dagger.json | oh:tin_dagger | The Item: oh:tin_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:721 ERROR] [Recipes] recipes/tin/tin_dagger.json | oh:tin_dagger | Recipe result malformed


[2025-09-05 12:04:41:721 ERROR] [Recipes] recipes/tin/tin_dagger.json | oh:tin_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:721 ERROR] [Recipes] recipes/tin/tin_head.json | Missing version tag


[2025-09-05 12:04:41:721 ERROR] [Recipes] recipes/tin/tin_battleaxe.json | oh:tin_battleaxe | The Item: oh:tin_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:721 ERROR] [Recipes] recipes/tin/tin_battleaxe.json | oh:tin_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:721 ERROR] [Recipes] recipes/tin/tin_battleaxe.json | oh:tin_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:722 ERROR] [Recipes] recipes/tin/tin_chestplate.json | Missing version tag


[2025-09-05 12:04:41:722 ERROR] [Recipes] recipes/mithril/mithril_apple.json | Missing version tag


[2025-09-05 12:04:41:722 ERROR] [Recipes] recipes/mithril/mithril_apple.json | oh:mithril_apple | The Item: oh:mithril_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:722 ERROR] [Recipes] recipes/mithril/mithril_apple.json | oh:mithril_apple | Recipe result malformed


[2025-09-05 12:04:41:722 ERROR] [Recipes] recipes/mithril/mithril_chestplate.json | Missing version tag


[2025-09-05 12:04:41:722 ERROR] [Recipes] recipes/mithril/mithril_leggings.json | Missing version tag


[2025-09-05 12:04:41:722 ERROR] [Recipes] recipes/mithril/mithril_block.json | Missing version tag


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_boots.json | Missing version tag


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_2.json | Missing version tag


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_dagger.json | oh:mithril_dagger | The Item: oh:mithril_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_dagger.json | oh:mithril_dagger | Recipe result malformed


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_dagger.json | oh:mithril_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_battleaxe.json | oh:mithril_battleaxe | The Item: oh:mithril_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_battleaxe.json | oh:mithril_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:723 ERROR] [Recipes] recipes/mithril/mithril_battleaxe.json | oh:mithril_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/mithril/mithril_helmet.json | Missing version tag


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/mithril/mithril_4.json | oh:mithril_4 | The Item: oh:raw_mithril is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/mithril/mithril_4.json | oh:mithril_4 | Furnace Recipe: oh:mithril_4 has an invalid input item

[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/mithril/mithril_head.json | Missing version tag


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/topaz/topaz_apple.json | Missing version tag


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/topaz/topaz_apple.json | oh:topaz_apple | The Item: oh:topaz_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/topaz/topaz_apple.json | oh:topaz_apple | Recipe result malformed


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/topaz/topaz_block.json | Missing version tag


[2025-09-05 12:04:41:724 ERROR] [Recipes] recipes/topaz/topaz_boots.json | Missing version tag


[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz_dagger.json | oh:topaz_dagger | The Item: oh:topaz_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz_dagger.json | oh:topaz_dagger | Recipe result malformed


[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz_dagger.json | oh:topaz_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz_head.json | Missing version tag


[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz_2.json | Missing version tag


[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz.json | oh:topaz | The Item: oh:raw_topaz is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz.json | oh:topaz | Furnace Recipe: oh:topaz has an invalid input item

[2025-09-05 12:04:41:725 ERROR] [Recipes] recipes/topaz/topaz_chestplate.json | Missing version tag


[2025-09-05 12:04:41:726 ERROR] [Recipes] recipes/topaz/topaz_battleaxe.json | oh:topaz_battleaxe | The Item: oh:topaz_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:726 ERROR] [Recipes] recipes/topaz/topaz_battleaxe.json | oh:topaz_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:726 ERROR] [Recipes] recipes/topaz/topaz_battleaxe.json | oh:topaz_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:726 ERROR] [Recipes] recipes/orichalcum/orichalcum_battleaxe.json | oh:orichalcum_battleaxe | The Item: oh:orichalcum_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:726 ERROR] [Recipes] recipes/orichalcum/orichalcum_battleaxe.json | oh:orichalcum_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:726 ERROR] [Recipes] recipes/orichalcum/orichalcum_battleaxe.json | oh:orichalcum_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:726 ERROR] [Recipes] recipes/orichalcum/orichalcum_2.json | Missing version tag


[2025-09-05 12:04:41:727 ERROR] [Recipes] recipes/orichalcum/orichalcum_boots.json | Missing version tag


[2025-09-05 12:04:41:727 ERROR] [Recipes] recipes/orichalcum/orichalcum_4.json | oh:orichalcum_4 | The Item: oh:raw_orichalcum is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:727 ERROR] [Recipes] recipes/orichalcum/orichalcum_4.json | oh:orichalcum_4 | Furnace Recipe: oh:orichalcum_4 has an invalid input item

[2025-09-05 12:04:41:727 ERROR] [Recipes] recipes/orichalcum/orichalcum_head.json | Missing version tag


[2025-09-05 12:04:41:727 ERROR] [Recipes] recipes/orichalcum/orichalcum_apple.json | Missing version tag


[2025-09-05 12:04:41:727 ERROR] [Recipes] recipes/orichalcum/orichalcum_apple.json | oh:orichalcum_apple | The Item: oh:orichalcum_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:727 ERROR] [Recipes] recipes/orichalcum/orichalcum_apple.json | oh:orichalcum_apple | Recipe result malformed


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/orichalcum/orichalcum_block.json | Missing version tag


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/orichalcum/orichalcum_chestplate.json | Missing version tag


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/orichalcum/orichalcum_helmet.json | Missing version tag


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/orichalcum/orichalcum_leggings.json | Missing version tag


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/orichalcum/orichalcum_dagger.json | oh:orichalcum_dagger | The Item: oh:orichalcum_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/orichalcum/orichalcum_dagger.json | oh:orichalcum_dagger | Recipe result malformed


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/orichalcum/orichalcum_dagger.json | oh:orichalcum_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/tourmaline/tourmaline_dagger.json | oh:tourmaline_dagger | The Item: oh:tourmaline_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/tourmaline/tourmaline_dagger.json | oh:tourmaline_dagger | Recipe result malformed


[2025-09-05 12:04:41:728 ERROR] [Recipes] recipes/tourmaline/tourmaline_dagger.json | oh:tourmaline_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:729 ERROR] [Recipes] recipes/tourmaline/tourmaline_battleaxe.json | oh:tourmaline_battleaxe | The Item: oh:tourmaline_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:729 ERROR] [Recipes] recipes/tourmaline/tourmaline_battleaxe.json | oh:tourmaline_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:729 ERROR] [Recipes] recipes/tourmaline/tourmaline_battleaxe.json | oh:tourmaline_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:729 ERROR] [Recipes] recipes/tourmaline/tourmaline_apple.json | Missing version tag


[2025-09-05 12:04:41:729 ERROR] [Recipes] recipes/tourmaline/tourmaline_apple.json | oh:tourmaline_apple | The Item: oh:tourmaline_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:729 ERROR] [Recipes] recipes/tourmaline/tourmaline_apple.json | oh:tourmaline_apple | Recipe result malformed


[2025-09-05 12:04:41:730 ERROR] [Recipes] recipes/tourmaline/tourmaline_chestplate.json | Missing version tag


[2025-09-05 12:04:41:730 ERROR] [Recipes] recipes/tourmaline/tourmaline_head.json | Missing version tag


[2025-09-05 12:04:41:730 ERROR] [Recipes] recipes/tourmaline/tourmaline_boots.json | Missing version tag


[2025-09-05 12:04:41:730 ERROR] [Recipes] recipes/tourmaline/tourmaline_block.json | Missing version tag


[2025-09-05 12:04:41:730 ERROR] [Recipes] recipes/tourmaline/tourmaline_2.json | Missing version tag


[2025-09-05 12:04:41:730 ERROR] [Recipes] recipes/misselanius_and_hammers/gold_coin.json | Missing version tag


[2025-09-05 12:04:41:731 ERROR] [Recipes] recipes/misselanius_and_hammers/amethyst_book.json | Missing version tag


[2025-09-05 12:04:41:731 ERROR] [Recipes] recipes/misselanius_and_hammers/amethyst_book.json | oh:amethyst_book | The Item: oh:amethyst_book is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:731 ERROR] [Recipes] recipes/misselanius_and_hammers/amethyst_book.json | oh:amethyst_book | Recipe result malformed


[2025-09-05 12:04:41:731 ERROR] [Recipes] recipes/misselanius_and_hammers/diamond_head.json | Missing version tag


[2025-09-05 12:04:41:731 ERROR] [Recipes] recipes/misselanius_and_hammers/gold_head.json | Missing version tag


[2025-09-05 12:04:41:731 ERROR] [Recipes] recipes/misselanius_and_hammers/stone_head.json | Missing version tag


[2025-09-05 12:04:41:732 ERROR] [Recipes] recipes/misselanius_and_hammers/gold_ingot_1.json | Missing version tag


[2025-09-05 12:04:41:732 ERROR] [Recipes] recipes/misselanius_and_hammers/iron_head.json | Missing version tag


[2025-09-05 12:04:41:732 ERROR] [Recipes] recipes/misselanius_and_hammers/wooden_head.json | Missing version tag


[2025-09-05 12:04:41:732 ERROR] [Recipes] recipes/aquarium/aquarium_2.json | Missing version tag


[2025-09-05 12:04:41:732 ERROR] [Recipes] recipes/aquarium/aquarium_leggings.json | Missing version tag


[2025-09-05 12:04:41:732 ERROR] [Recipes] recipes/aquarium/aquarium_4.json | oh:aquarium_4 | The Item: oh:raw_aquarium is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:732 ERROR] [Recipes] recipes/aquarium/aquarium_4.json | oh:aquarium_4 | Furnace Recipe: oh:aquarium_4 has an invalid input item

[2025-09-05 12:04:41:733 ERROR] [Recipes] recipes/aquarium/aquarium_battleaxe.json | oh:aquarium_battleaxe | The Item: oh:aquarium_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:733 ERROR] [Recipes] recipes/aquarium/aquarium_battleaxe.json | oh:aquarium_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:733 ERROR] [Recipes] recipes/aquarium/aquarium_battleaxe.json | oh:aquarium_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:733 ERROR] [Recipes] recipes/aquarium/aquarium_block.json | Missing version tag


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_boots.json | Missing version tag


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_chestplate.json | Missing version tag


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_apple.json | Missing version tag


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_apple.json | oh:aquarium_apple | The Item: oh:aquarium_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_apple.json | oh:aquarium_apple | Recipe result malformed


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_dagger.json | oh:aquarium_dagger | The Item: oh:aquarium_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_dagger.json | oh:aquarium_dagger | Recipe result malformed


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_dagger.json | oh:aquarium_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_head.json | Missing version tag


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/aquarium/aquarium_helmet.json | Missing version tag


[2025-09-05 12:04:41:734 ERROR] [Recipes] recipes/silver/silver_helmet.json | Missing version tag


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_head.json | Missing version tag


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_apple.json | Missing version tag


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_apple.json | oh:silver_apple | The Item: oh:silver_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_apple.json | oh:silver_apple | Recipe result malformed


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_battleaxe.json | oh:silver_battleaxe | The Item: oh:silver_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_battleaxe.json | oh:silver_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_battleaxe.json | oh:silver_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_4.json | oh:silver_4 | The Item: oh:raw_silver is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_4.json | oh:silver_4 | Furnace Recipe: oh:silver_4 has an invalid input item

[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_dagger.json | oh:silver_dagger | The Item: oh:silver_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_dagger.json | oh:silver_dagger | Recipe result malformed


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_dagger.json | oh:silver_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:735 ERROR] [Recipes] recipes/silver/silver_2.json | Missing version tag


[2025-09-05 12:04:41:736 ERROR] [Recipes] recipes/silver/silver_block.json | Missing version tag


[2025-09-05 12:04:41:736 ERROR] [Recipes] recipes/silver/silver_leggings.json | Missing version tag


[2025-09-05 12:04:41:736 ERROR] [Recipes] recipes/silver/silver_chestplate.json | Missing version tag


[2025-09-05 12:04:41:736 ERROR] [Recipes] recipes/silver/silver_boots.json | Missing version tag


[2025-09-05 12:04:41:736 ERROR] [Recipes] recipes/ruby/ruby_leggings.json | Missing version tag


[2025-09-05 12:04:41:737 ERROR] [Recipes] recipes/ruby/ruby_block.json | Missing version tag


[2025-09-05 12:04:41:737 ERROR] [Recipes] recipes/ruby/ruby_helmet.json | Missing version tag


[2025-09-05 12:04:41:737 ERROR] [Recipes] recipes/ruby/ruby_head.json | Missing version tag


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_2.json | Missing version tag


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_dagger.json | oh:ruby_dagger | The Item: oh:ruby_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_dagger.json | oh:ruby_dagger | Recipe result malformed


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_dagger.json | oh:ruby_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_boots.json | Missing version tag


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_apple.json | Missing version tag


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_apple.json | oh:ruby_apple | The Item: oh:ruby_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_apple.json | oh:ruby_apple | Recipe result malformed


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_chestplate.json | Missing version tag


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_battleaxe.json | oh:ruby_battleaxe | The Item: oh:ruby_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_battleaxe.json | oh:ruby_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:738 ERROR] [Recipes] recipes/ruby/ruby_battleaxe.json | oh:ruby_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_apple.json | Missing version tag


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_apple.json | oh:onyx_apple | The Item: oh:onyx_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_apple.json | oh:onyx_apple | Recipe result malformed


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_battleaxe.json | oh:onyx_battleaxe | The Item: oh:onyx_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_battleaxe.json | oh:onyx_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_battleaxe.json | oh:onyx_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_boots.json | Missing version tag


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_helmet.json | Missing version tag


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/quartz_head.json | Missing version tag


[2025-09-05 12:04:41:739 ERROR] [Recipes] recipes/onyx/onyx_head.json | Missing version tag


[2025-09-05 12:04:41:740 ERROR] [Recipes] recipes/onyx/onyx_chestplate.json | Missing version tag


[2025-09-05 12:04:41:740 ERROR] [Recipes] recipes/onyx/onyx_block.json | Missing version tag


[2025-09-05 12:04:41:740 ERROR] [Recipes] recipes/onyx/onyx_dagger.json | oh:onyx_dagger | The Item: oh:onyx_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:740 ERROR] [Recipes] recipes/onyx/onyx_dagger.json | oh:onyx_dagger | Recipe result malformed


[2025-09-05 12:04:41:740 ERROR] [Recipes] recipes/onyx/onyx_dagger.json | oh:onyx_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:740 ERROR] [Recipes] recipes/onyx/onyx_2.json | Missing version tag


[2025-09-05 12:04:41:741 ERROR] [Recipes] recipes/onyx/onyx_leggings.json | Missing version tag


[2025-09-05 12:04:41:741 ERROR] [Recipes] recipes/dramantite/dramantite_block.json | Missing version tag


[2025-09-05 12:04:41:741 ERROR] [Recipes] recipes/dramantite/dramantite_battleaxe.json | oh:dramantite_battleaxe | The Item: oh:dramantite_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:741 ERROR] [Recipes] recipes/dramantite/dramantite_battleaxe.json | oh:dramantite_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:741 ERROR] [Recipes] recipes/dramantite/dramantite_battleaxe.json | oh:dramantite_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:741 ERROR] [Recipes] recipes/dramantite/dramantite_2.json | Missing version tag


[2025-09-05 12:04:41:742 ERROR] [Recipes] recipes/dramantite/dramantite_head.json | Missing version tag


[2025-09-05 12:04:41:742 ERROR] [Recipes] recipes/dramantite/dramantite_apple.json | Missing version tag


[2025-09-05 12:04:41:742 ERROR] [Recipes] recipes/dramantite/dramantite_apple.json | oh:dramantite_apple | The Item: oh:dramantite_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:742 ERROR] [Recipes] recipes/dramantite/dramantite_apple.json | oh:dramantite_apple | Recipe result malformed


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/dramantite/dramantite_boots.json | Missing version tag


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/dramantite/dramantite_dagger.json | oh:dramantite_dagger | The Item: oh:dramantite_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/dramantite/dramantite_dagger.json | oh:dramantite_dagger | Recipe result malformed


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/dramantite/dramantite_dagger.json | oh:dramantite_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/dramantite/dramantite_chestplate.json | Missing version tag


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/titanium/titanium_2.json | Missing version tag


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/titanium/titanium_boots.json | Missing version tag


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/titanium/titanium_battleaxe.json | oh:titanium_battleaxe | The Item: oh:titanium_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/titanium/titanium_battleaxe.json | oh:titanium_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/titanium/titanium_battleaxe.json | oh:titanium_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:743 ERROR] [Recipes] recipes/titanium/titanium_head.json | Missing version tag


[2025-09-05 12:04:41:744 ERROR] [Recipes] recipes/titanium/titanium_apple.json | Missing version tag


[2025-09-05 12:04:41:744 ERROR] [Recipes] recipes/titanium/titanium_apple.json | oh:titanium_apple | The Item: oh:titanium_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:744 ERROR] [Recipes] recipes/titanium/titanium_apple.json | oh:titanium_apple | Recipe result malformed


[2025-09-05 12:04:41:744 ERROR] [Recipes] recipes/titanium/titanium_block.json | Missing version tag


[2025-09-05 12:04:41:745 ERROR] [Recipes] recipes/titanium/titanium_chestplate.json | Missing version tag


[2025-09-05 12:04:41:745 ERROR] [Recipes] recipes/titanium/titanium_dagger.json | oh:titanium_dagger | The Item: oh:titanium_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:745 ERROR] [Recipes] recipes/titanium/titanium_dagger.json | oh:titanium_dagger | Recipe result malformed


[2025-09-05 12:04:41:745 ERROR] [Recipes] recipes/titanium/titanium_dagger.json | oh:titanium_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:745 ERROR] [Recipes] recipes/migtinio/migtinio_chestplate.json | Missing version tag


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_leggings.json | Missing version tag


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_2.json | Missing version tag


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_head.json | Missing version tag


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_dagger.json | oh:migtinio_dagger | The Item: oh:migtinio_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_dagger.json | oh:migtinio_dagger | Recipe result malformed


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_dagger.json | oh:migtinio_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_apple.json | Missing version tag


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_apple.json | oh:migtinio_apple | The Item: oh:migtinio_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_apple.json | oh:migtinio_apple | Recipe result malformed


[2025-09-05 12:04:41:746 ERROR] [Recipes] recipes/migtinio/migtinio_helmet.json | Missing version tag


[2025-09-05 12:04:41:747 ERROR] [Recipes] recipes/migtinio/migtinio_block.json | Missing version tag


[2025-09-05 12:04:41:747 ERROR] [Recipes] recipes/migtinio/migtinio_battleaxe.json | oh:migtinio_battleaxe | The Item: oh:migtinio_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:747 ERROR] [Recipes] recipes/migtinio/migtinio_battleaxe.json | oh:migtinio_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:747 ERROR] [Recipes] recipes/migtinio/migtinio_battleaxe.json | oh:migtinio_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:747 ERROR] [Recipes] recipes/migtinio/migtinio_boots.json | Missing version tag


[2025-09-05 12:04:41:748 ERROR] [Recipes] recipes/amethyst/amethyst_head.json | oh:amethyst_head | The Item: oh:amethyst_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:748 ERROR] [Recipes] recipes/amethyst/amethyst_head.json | oh:amethyst_head | Recipe result malformed


[2025-09-05 12:04:41:748 ERROR] [Recipes] recipes/amethyst/amethyst_hammer.json | oh:amethyst_hammer | The Item: oh:amethyst_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:748 ERROR] [Recipes] recipes/amethyst/amethyst_hammer.json | oh:amethyst_hammer | Recipe for: oh:amethyst_hammer is missing (unknown) ingredient


[2025-09-05 12:04:41:748 ERROR] [Recipes] recipes/enderite/enderite_dagger.json | oh:enderite_dagger | The Item: oh:enderite_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:748 ERROR] [Recipes] recipes/enderite/enderite_dagger.json | oh:enderite_dagger | Recipe result malformed


[2025-09-05 12:04:41:748 ERROR] [Recipes] recipes/enderite/enderite_dagger.json | oh:enderite_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:749 ERROR] [Recipes] recipes/enderite/enderite.json | Missing version tag


[2025-09-05 12:04:41:749 ERROR] [Recipes] recipes/enderite/enderite_leggings.json | Missing version tag


[2025-09-05 12:04:41:749 ERROR] [Recipes] recipes/enderite/enderite_head.json | Missing version tag


[2025-09-05 12:04:41:749 ERROR] [Recipes] recipes/enderite/enderite_helmet.json | Missing version tag


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_boots.json | Missing version tag


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_battleaxe.json | oh:enderite_battleaxe | The Item: oh:enderite_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_battleaxe.json | oh:enderite_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_battleaxe.json | oh:enderite_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_block.json | Missing version tag


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_apple.json | Missing version tag


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_apple.json | oh:enderite_apple | The Item: oh:enderite_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_apple.json | oh:enderite_apple | Recipe result malformed


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_chestplate.json | Missing version tag


[2025-09-05 12:04:41:750 ERROR] [Recipes] recipes/enderite/enderite_2.json | Missing version tag


[2025-09-05 12:04:41:751 ERROR] [Recipes] recipes/platinum/platinum_chestplate.json | Missing version tag


[2025-09-05 12:04:41:751 ERROR] [Recipes] recipes/platinum/platinum_boots.json | Missing version tag


[2025-09-05 12:04:41:751 ERROR] [Recipes] recipes/platinum/platinum_head.json | Missing version tag


[2025-09-05 12:04:41:751 ERROR] [Recipes] recipes/platinum/platinum_2.json | Missing version tag


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_block.json | Missing version tag


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_dagger.json | oh:platinum_dagger | The Item: oh:platinum_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_dagger.json | oh:platinum_dagger | Recipe result malformed


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_dagger.json | oh:platinum_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_battleaxe.json | oh:platinum_battleaxe | The Item: oh:platinum_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_battleaxe.json | oh:platinum_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_battleaxe.json | oh:platinum_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_apple.json | Missing version tag


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_apple.json | oh:platinum_apple | The Item: oh:platinum_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:752 ERROR] [Recipes] recipes/platinum/platinum_apple.json | oh:platinum_apple | Recipe result malformed


[2025-09-05 12:04:41:753 ERROR] [Recipes] recipes/jade/jade_leggings.json | Missing version tag


[2025-09-05 12:04:41:753 ERROR] [Recipes] recipes/jade/jade_battleaxe.json | oh:jade_battleaxe | The Item: oh:jade_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:753 ERROR] [Recipes] recipes/jade/jade_battleaxe.json | oh:jade_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:753 ERROR] [Recipes] recipes/jade/jade_battleaxe.json | oh:jade_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:753 ERROR] [Recipes] recipes/jade/jade_dagger.json | oh:jade_dagger | The Item: oh:jade_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:753 ERROR] [Recipes] recipes/jade/jade_dagger.json | oh:jade_dagger | Recipe result malformed


[2025-09-05 12:04:41:753 ERROR] [Recipes] recipes/jade/jade_dagger.json | oh:jade_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_boots.json | Missing version tag


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_helmet.json | Missing version tag


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_apple.json | Missing version tag


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_apple.json | oh:jade_apple | The Item: oh:jade_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_apple.json | oh:jade_apple | Recipe result malformed


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_head.json | Missing version tag


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_chestplate.json | Missing version tag


[2025-09-05 12:04:41:754 ERROR] [Recipes] recipes/jade/jade_2.json | Missing version tag


[2025-09-05 12:04:41:755 ERROR] [Recipes] recipes/jade/jade_block.json | Missing version tag


[2025-09-05 12:04:41:755 ERROR] [Recipes] recipes/paladium/paladium_battleaxe.json | oh:paladium_battleaxe | The Item: oh:paladium_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:755 ERROR] [Recipes] recipes/paladium/paladium_battleaxe.json | oh:paladium_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:755 ERROR] [Recipes] recipes/paladium/paladium_battleaxe.json | oh:paladium_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:755 ERROR] [Recipes] recipes/paladium/paladium_head.json | Missing version tag


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_block.json | Missing version tag


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_2.json | Missing version tag


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_apple.json | Missing version tag


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_apple.json | oh:paladium_apple | The Item: oh:paladium_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_apple.json | oh:paladium_apple | Recipe result malformed


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_chestplate.json | Missing version tag


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_boots.json | Missing version tag


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_dagger.json | oh:paladium_dagger | The Item: oh:paladium_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_dagger.json | oh:paladium_dagger | Recipe result malformed


[2025-09-05 12:04:41:756 ERROR] [Recipes] recipes/paladium/paladium_dagger.json | oh:paladium_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_boots.json | Missing version tag


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_hammer.json | oh:bronce_hammer | The Item: oh:bronce_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_hammer.json | oh:bronce_hammer | Recipe for: oh:bronce_hammer is missing (unknown) ingredient


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_battleaxe.json | oh:bronce_battleaxe | The Item: oh:bronce_battleaxe is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_battleaxe.json | oh:bronce_battleaxe | Recipe result malformed


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_battleaxe.json | oh:bronce_battleaxe | Recipe has no result item(s)


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_apple.json | Missing version tag


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_apple.json | oh:bronce_apple | The Item: oh:bronce_apple is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_apple.json | oh:bronce_apple | Recipe result malformed


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_dagger.json | oh:bronce_dagger | The Item: oh:bronce_dagger is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_dagger.json | oh:bronce_dagger | Recipe result malformed


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_dagger.json | oh:bronce_dagger | Recipe has no result item(s)


[2025-09-05 12:04:41:757 ERROR] [Recipes] recipes/bronze/bronce_2.json | Missing version tag


[2025-09-05 12:04:41:758 ERROR] [Recipes] recipes/bronze/bronce.json | Missing version tag


[2025-09-05 12:04:41:758 ERROR] [Recipes] recipes/bronze/bronce_leggings.json | Missing version tag


[2025-09-05 12:04:41:758 ERROR] [Recipes] recipes/bronze/bronce_block.json | Missing version tag


[2025-09-05 12:04:41:758 ERROR] [Recipes] recipes/bronze/bronce_helmet.json | Missing version tag


[2025-09-05 12:04:41:758 ERROR] [Recipes] recipes/bronze/bronce_head.json | Missing version tag


[2025-09-05 12:04:41:758 ERROR] [Recipes] recipes/bronze/bronce_head.json | oh:bronce_head | The Item: oh:bronce_head is missing or invalid, can't make the recipe


[2025-09-05 12:04:41:758 ERROR] [Recipes] recipes/bronze/bronce_head.json | oh:bronce_head | Recipe result malformed


[2025-09-05 12:04:41:759 ERROR] [Recipes] recipes/bronze/bronce_chestplate.json | Missing version tag


[2025-09-05 12:04:41:862 WARN] [Recipes] recipes/iron_bars.json | minecraft:iron_bars | Recipe "minecraft:iron_bars" has the same ingredients as oh:iron_head's recipe but outputs minecraft:iron_bars instead. Adding duplicate crafting_table recipe.

[2025-09-05 12:04:41:874 WARN] [Recipes] recipes/gold_nugget.json | minecraft:gold_nugget | Recipe "minecraft:gold_nugget" has the same ingredients as oh:gold_coin's recipe but outputs minecraft:gold_nugget instead. Adding duplicate crafting_table recipe.

[2025-09-05 12:04:42:103 INFO] IPv4 supported, port: 19132: Used for gameplay and LAN discovery
[2025-09-05 12:04:42:103 INFO] IPv6 supported, port: 19133: Used for gameplay
[2025-09-05 12:04:42:661 WARN] [Commands] Function barrel_5 failed to load correctly with error(s):

[2025-09-05 12:04:42:661 WARN] [Commands] Error on line 1: command failed to parse with error 'Syntax error: Unexpected "oh:barrel_without_gold": at "~ ~ ~ ~ ~ >>oh:barrel_without_gold<< ["oh:faci"'

[2025-09-05 12:04:42:662 WARN] [Commands] Function barrel_3 failed to load correctly with error(s):

[2025-09-05 12:04:42:662 WARN] [Commands] Error on line 1: command failed to parse with error 'Syntax error: Unexpected "oh:barrel_without_gold": at "~ ~ ~ ~ ~ >>oh:barrel_without_gold<< ["oh:faci"'

[2025-09-05 12:04:42:662 WARN] [Commands] Function barrel_1 failed to load correctly with error(s):

[2025-09-05 12:04:42:662 WARN] [Commands] Error on line 1: command failed to parse with error 'Syntax error: Unexpected "oh:barrel_without_gold": at "~ ~ ~ ~ ~ >>oh:barrel_without_gold<< ["oh:faci"'

[2025-09-05 12:04:42:662 WARN] [Commands] Function barrel_0 failed to load correctly with error(s):

[2025-09-05 12:04:42:662 WARN] [Commands] Error on line 1: command failed to parse with error 'Syntax error: Unexpected "oh:barrel_without_gold": at "~ ~ ~ ~ ~ >>oh:barrel_without_gold<< ["oh:faci"'

[2025-09-05 12:04:42:662 WARN] [Commands] Function barrel_2 failed to load correctly with error(s):

[2025-09-05 12:04:42:662 WARN] [Commands] Error on line 1: command failed to parse with error 'Syntax error: Unexpected "oh:barrel_without_gold": at "~ ~ ~ ~ ~ >>oh:barrel_without_gold<< ["oh:faci"'

[2025-09-05 12:04:42:666 WARN] [Commands] Function copper failed to load correctly with error(s):

[2025-09-05 12:04:42:666 WARN] [Commands] Error on line 1: command failed to parse with error 'Syntax error: Unexpected "oh:copper_boots": at "feet,item=>>oh:copper_boots<<}] resista"'

[2025-09-05 12:04:42:666 WARN] [Commands] Error on line 2: command failed to parse with error 'Syntax error: Unexpected "oh:copper_chestplate": at "hest,item=>>oh:copper_chestplate<<}] regener"'

[2025-09-05 12:04:42:666 WARN] [Commands] Error on line 3: command failed to parse with error 'Syntax error: Unexpected "oh:copper_helme