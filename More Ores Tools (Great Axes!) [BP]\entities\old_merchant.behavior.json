{"format_version": "1.18.10", "minecraft:entity": {"description": {"identifier": "oh:old_merchant", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"summon": {"minecraft:damage_sensor": {"triggers": [{"cause": "all", "on_damage": {"target": "self", "event": "animation_x"}}]}, "minecraft:timer": {"time": 10, "looping": false, "time_down_event": {"event": "summon_fin", "target": "self"}}}, "detect": {"minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "operator": "not", "value": "villager"}]}, "target": "self", "event": "deffend"}}]}}, "spawn": {"minecraft:type_family": {"family": ["inanimate"]}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:fire_immune": {}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": false, "on_damage": {"event": "block", "target": "self"}}]}, "minecraft:timer": {"time": 3, "looping": true, "time_down_event": {"event": "event_particle", "target": "self"}}, "minecraft:mark_variant": {"value": 0}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}]}, "event": "attentive"}}]}}, "attentive": {"minecraft:type_family": {"family": ["old_merchant", "villager"]}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:mark_variant": {"value": 1}, "minecraft:behavior.trade_interest": {"priority": 3, "within_radius": 6, "interest_time": 45, "remove_item_time": 1, "carried_item_switch_time": 2, "cooldown": 2}, "minecraft:economy_trade_table": {"display_name": "Old_Merchant", "table": "trading/economy_trades/old_merchant_trades.json", "new_screen": true}, "minecraft:behavior.look_at_trading_player": {"priority": 4}, "minecraft:behavior.trade_with_player": {"priority": 1}, "minecraft:movement": {"value": 0.4}, "minecraft:timer": {"time": 25, "looping": false, "time_down_event": {"event": "desattentive", "target": "self"}}}}, "components": {"minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:health": {"value": 40, "max": 40}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:nameable": {}, "minecraft:navigation.walk": {"can_path_over_water": true, "can_pass_doors": true, "can_open_doors": false, "avoid_water": true}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:persistent": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:physics": {}, "minecraft:behavior.look_at_player": {"priority": 8, "look_distance": 8, "probability": 0.02}}, "events": {"animation_x": {}, "block": {}, "summon_fin": {"remove": {"component_groups": ["summon"]}, "add": {"component_groups": ["detect", "attentive"]}}, "deffend": {"add": {"component_groups": ["summon"]}, "remove": {"component_groups": ["detect"]}}, "event_particle": {}, "minecraft:entity_spawned": {"add": {"component_groups": ["spawn"]}}, "desattentive": {"remove": {"component_groups": ["attentive", "detect"]}, "add": {"component_groups": ["spawn"]}}, "attentive": {"add": {"component_groups": ["attentive", "detect"]}, "remove": {"component_groups": ["spawn"]}}}}}