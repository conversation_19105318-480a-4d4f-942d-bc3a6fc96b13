import { world, Player, EquipmentSlot, system, EntityEquippableComponent, ItemStack, GameMode } from '@minecraft/server';


function break3x3Area(player, blockLocation) {
    if (!blockLocation) return;
    if (player.isSneaking) {
        player.runCommandAsync(`title @s actionbar §o§cYou §l§4cannot§r §c§ouse this tool while §l§4sneaking§r§c!`);
        return;
    }

    const rotation = player.getRotation();
    const yaw = rotation.y; // Mirada horizontal
    const pitch = rotation.x; // Mirada vertical

    const blocksToBreak = [];

    if (pitch <= -45 || pitch >= 45) {
        // Mirando hacia arriba o abajo: romper en plano XZ
        for (let dx = -1; dx <= 1; dx++) {
            for (let dz = -1; dz <= 1; dz++) {
                blocksToBreak.push({
                    x: blockLocation.x + dx,
                    y: blockLocation.y,
                    z: blockLocation.z + dz
                });
            }
        }
    } else {
        // Mirada horizontal: usar yaw
        let axis;
        if ((yaw >= -45 && yaw < 45) || (yaw >= 135 || yaw < -135)) {
            // Mirando norte o sur → plano XY (vertical en X)
            axis = "XY";
        } else {
            // Mirando este u oeste → plano YZ (vertical en Z)
            axis = "YZ";
        }

        for (let dy = -1; dy <= 1; dy++) {
            for (let d = -1; d <= 1; d++) {
                let target;
                if (axis === "XY") {
                    target = {
                        x: blockLocation.x + d,
                        y: blockLocation.y + dy,
                        z: blockLocation.z
                    };
                } else {
                    target = {
                        x: blockLocation.x,
                        y: blockLocation.y + dy,
                        z: blockLocation.z + d
                    };
                }
                blocksToBreak.push(target);
            }
        }
    }

    // Rompe los bloques excepto si son irrompibles
    for (const target of blocksToBreak) {
        const command = `execute unless block ${target.x} ${target.y} ${target.z} vault unless block ${target.x} ${target.y} ${target.z} structure_block unless block ${target.x} ${target.y} ${target.z} trial_spawner unless block ${target.x} ${target.y} ${target.z} bedrock unless block ${target.x} ${target.y} ${target.z} light_block unless block ${target.x} ${target.y} ${target.z} barrier unless block ${target.x} ${target.y} ${target.z} deny unless block ${target.x} ${target.y} ${target.z} allow unless block ${target.x} ${target.y} ${target.z} command_block unless block ${target.x} ${target.y} ${target.z} obsidian unless block ${target.x} ${target.y} ${target.z} border_block unless block ${target.x} ${target.y} ${target.z} crying_obsidian unless block ${target.x} ${target.y} ${target.z} reinforced_deepslate run setblock ${target.x} ${target.y} ${target.z} air destroy`;
        player.runCommandAsync(command);
    }
}

world.afterEvents.playerBreakBlock.subscribe(dcd => {
    const { player, itemStackBeforeBreak: itemUsed, block } = dcd;

    // This returns if itemUsed is undefined.
    if (!itemUsed) return;

    // Detect if the item belongs to the addon "oh:"
    if (itemUsed.typeId.startsWith('oh:')) {
        // Ejecutar comando si es un martillo
        if (itemUsed.typeId.includes('_hammer')) {
            break3x3Area(player, block.location);
        }

        // This retrieves the player's equippable component.
        const playerEquippableComp = player.getComponent("equippable");

        // This returns if playerEquippableComp is undefined.
        if (!playerEquippableComp) return;

        // This retrieves the enchantable component of the item.
        const itemEnchantmentComp = itemUsed.getComponent("minecraft:enchantable");
        const unbreakingLevel = itemEnchantmentComp?.getEnchantment("unbreaking")?.level ?? 0;

        // Calculates the chance of an item breaking based on its unbreaking level. This is the vanilla unbreaking formula.
        const breakChance = 100 / (unbreakingLevel + 1);
        // Generates a random chance value between 0 and 100.
        const randomizeChance = Math.random() * 100;

        // This returns if breakChance is less than randomizeChance.
        if (breakChance < randomizeChance) return;

        // This retrieves the durability component of the item.
        const itemUsedDurabilityComp = itemUsed.getComponent("durability");

        // This returns if itemUsedDurabilityComp is undefined.
        if (!itemUsedDurabilityComp) return;

        let durabilityModifier = 0;
        if (itemUsed.typeId.includes('shovel') || itemUsed.typeId.includes('axe') || itemUsed.typeId.includes('pickaxe') || itemUsed.typeId.includes('hoe')) {
            // If the item is a tool, then it will set the durability modifier to 1.
            if (itemUsed.typeId.includes('tin_sword')) {
                onGreatSwordHit(player);
            }
            durabilityModifier = 1;
        } else if (itemUsed.typeId.includes('sword') || itemUsed.typeId.includes('_hammer')) {
            // If the item is a weapon or a hammer, then it will set the durability modifier to 2.
            durabilityModifier = 1;
        }

        // This will set the new durability value.
        itemUsedDurabilityComp.damage += durabilityModifier;

        // Declares and checks if the item is out of durability
        const maxDurability = itemUsedDurabilityComp.maxDurability;
        const currentDamage = itemUsedDurabilityComp.damage;
        if (currentDamage >= maxDurability) {

            // If the item is out of durability, plays the item breaking sound and removes the item
            player.playSound('random.break', { pitch: 1, location: player.location, volume: 1 });
            playerEquippableComp.setEquipment("Mainhand", new ItemStack('minecraft:air', 1));
        } else if (currentDamage < maxDurability) {

            // This sets the item in the player's selected slot.
            playerEquippableComp.setEquipment("Mainhand", itemUsed);
        }
    }
});

world.afterEvents.entityHurt.subscribe(ev => {
    const { damageSource, hurtEntity } = ev;
    const attacker = damageSource.damagingEntity;

    // Asegurarse de que el atacante es un jugador
    if (!attacker || attacker.typeId !== "minecraft:player") return;

    // Solo si el jugador tiene el tag
    const validTags = [
        "have_great_sword_1",
        "have_great_sword_2",
        "have_great_sword_3",
        "have_great_sword_4",
        "have_great_sword_5",
        "have_great_sword_6",
        "have_great_sword_7",
        "have_great_sword_8",
        "have_great_sword_9",
        "have_great_sword_10",
        "have_great_sword_11",
        "have_great_sword_12",
        "have_great_sword_13",
        "have_great_sword_14",
        "have_great_sword_15",
        "have_great_sword_16",
        "have_great_sword_17",
        "have_great_sword_18"
    ];

    if (validTags.some(tag => attacker.hasTag(tag))) {
        // Ejecutar comando desde la entidad golpeada
        hurtEntity.runCommandAsync(`summon oh:sweep ~ ~1 ~`);
        attacker.runCommandAsync(`particle oh:sweep_p_m ^ ^1.2 ^2`);
        attacker.runCommandAsync(`playsound sound.sweep @a ~ ~ ~ 1`);
    }
});
