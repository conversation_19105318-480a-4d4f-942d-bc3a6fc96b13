{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "oh:ores_trader", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"managed": {"minecraft:managed_wandering_trader": {}}, "despawning": {"minecraft:type_family": {"family": ["wandering_trader", "wandering_trader_despawning", "mob"]}}, "minecraft:scared": {"minecraft:angry": {"duration": 5, "broadcastAnger": true, "broadcastRange": 10, "broadcast_targets": ["fox"], "broadcast_filters": {"test": "is_leashed_to", "subject": "other", "value": true}, "calm_event": {"event": "minecraft:become_calm", "target": "self"}}}}, "components": {"minecraft:type_family": {"family": ["wandering_trader", "mob"]}, "minecraft:is_hidden_when_invisible": {}, "minecraft:timer": {"looping": false, "random_time_choices": [{"weight": 50, "value": 2400}, {"weight": 50, "value": 3600}], "time_down_event": {"event": "minecraft:start_despawn", "target": "self"}}, "minecraft:spawn_entity": {"entities": [{"min_wait_time": 0, "max_wait_time": 0, "spawn_entity": "fox", "spawn_event": "minecraft:from_wandering_trader", "single_use": true, "num_to_spawn": 2, "should_leash": true}]}, "minecraft:economy_trade_table": {"display_name": "Ores Trader", "table": "trading/economy_trades/ores_trader_trades.json", "new_screen": true}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:movement": {"value": 0.5}, "minecraft:navigation.walk": {"can_path_over_water": true, "can_pass_doors": true, "can_open_doors": true, "avoid_water": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:despawn": {"remove_child_entities": true, "filters": {"all_of": [{"any_of": [{"test": "is_family", "subject": "self", "value": "wandering_trader_despawning"}, {"test": "has_trade_supply", "subject": "self", "value": false}]}, {"test": "distance_to_nearest_player", "operator": ">", "value": 24}]}}, "minecraft:damage_sensor": {"triggers": [{"cause": "entity_attack", "deals_damage": true, "on_damage": {"event": "minecraft:become_scared"}}, {"cause": "projectile", "deals_damage": true, "on_damage": {"event": "minecraft:become_scared"}}, {"cause": "magic", "deals_damage": true, "on_damage": {"event": "minecraft:become_scared"}}]}, "minecraft:behavior.trade_with_player": {"priority": 1}, "minecraft:behavior.trade_interest": {"priority": 3, "within_radius": 6, "interest_time": 45, "remove_item_time": 1, "carried_item_switch_time": 2, "cooldown": 2}, "minecraft:behavior.look_at_trading_player": {"priority": 4}, "minecraft:behavior.panic": {"priority": 1, "speed_multiplier": 0.6}, "minecraft:behavior.drink_potion": {"priority": 1, "speed_modifier": -0.2, "potions": [{"id": 7, "chance": 1, "filters": {"all_of": [{"any_of": [{"test": "hourly_clock_time", "operator": ">=", "value": 18000}, {"test": "hourly_clock_time", "operator": "<", "value": 12000}]}, {"test": "is_visible", "subject": "self", "value": true}, {"any_of": [{"test": "is_avoiding_mobs", "subject": "self", "value": true}, {"all_of": [{"test": "has_component", "subject": "self", "value": "minecraft:angry"}, {"test": "is_family", "subject": "target", "operator": "!=", "value": "player"}]}]}]}}, {"id": 8, "chance": 1, "filters": {"all_of": [{"test": "hourly_clock_time", "operator": ">=", "value": 12000}, {"test": "hourly_clock_time", "operator": "<", "value": 18000}, {"test": "is_visible", "subject": "self", "value": true}, {"any_of": [{"test": "is_avoiding_mobs", "subject": "self", "value": true}, {"test": "has_component", "subject": "self", "value": "minecraft:angry"}]}]}}]}, "minecraft:behavior.avoid_mob_type": {"priority": 2, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_pigman"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "vex"}]}, "walk_speed_multiplier": 0.6, "sprint_speed_multiplier": 0.6}], "max_dist": 6}, "minecraft:behavior.restrict_open_door": {"priority": 5}, "minecraft:behavior.open_door": {"priority": 6, "close_door_after": true}, "minecraft:behavior.look_at_player": {"priority": 8, "look_distance": 8, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"minecraft:scheduled": {"add": {"component_groups": ["managed"]}}, "minecraft:start_despawn": {"add": {"component_groups": ["despawning"]}}, "minecraft:become_scared": {"add": {"component_groups": ["minecraft:scared"]}}, "minecraft:become_calm": {"remove": {"component_groups": ["minecraft:scared"]}}}}}